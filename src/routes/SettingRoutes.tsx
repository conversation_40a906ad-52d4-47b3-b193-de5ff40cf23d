import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import AccountSettings from '@/pages/settings/accountSettings/AccountSettings';

export const SettingsRoutes: RouteObject[] = [
  {
    path: ROUTES.SETTINGS.SETTINGS_ACCOUNT,
    element: (
      <ProtectedRoute
        element={<AccountSettings />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.SETTINGS_GENERAL}
      />
    ),
  },
];
