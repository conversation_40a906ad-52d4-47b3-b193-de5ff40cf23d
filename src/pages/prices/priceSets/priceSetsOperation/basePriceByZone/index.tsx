import SelectDownArrow from '@/assets/icons/selectDownArrow';
import { useLanguage } from '@/hooks/useLanguage';
import { IAdjustCellsType, IZoneLookupRow } from '@/pages/location/zone/list/zone.type';
import { Button, Checkbox, Form, InputNumber, Select, Typography } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { logger } from '@/lib/logger/logger';
import { EmptyStatePage } from '@/components/common/emptyState/EmptyStatePage';
import Spinner from '@/components/common/spinner/Spinner';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { priceSetHook, priceSetService } from '@/api/priceSet/usePriceSet';
import { BasePriceByZoneDTO, ZoneTableValues } from '@/api/priceSet/priceSet.types';
import { zoneTableHook, zoneTableService } from '@/api/zoneTable/useZoneTable';
import { zoneHooks } from '@/api/zones/useZones';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ArrowSvgIcon } from '@/assets';
import { EditOutlined } from '@ant-design/icons';
import CustomModal from '@/components/common/modal/CustomModal';

const { Text } = Typography;

const BasePriceByZone: React.FC = () => {
  const {
    data: zoneTables,
    isFetching,
    isLoading,
  } = zoneTableHook.useList({ pageNumber: 1, pageSize: 100 });

  const { data: zones } = zoneHooks.useList({ pageNumber: 1, pageSize: 100 });
  const { t } = useLanguage();
  const gridRef = useRef<HTMLDivElement>(null);
  const notificationManager = useNotificationManager();
  const { id: priceSetId } = useParams();

  // Active Cell and Interaction State
  const [activeCell, setActiveCell] = useState<{ row: number; col: number } | null>(null);
  const [highlight, setHighlight] = useState(t('zonePage.zoneLookUp.hoverOverACell'));
  const [cellValues, setCellValues] = useState<IZoneLookupRow[]>([]);
  const [undoStack, setUndoStack] = useState<IZoneLookupRow[][]>([]);
  const [redoStack, setRedoStack] = useState<IZoneLookupRow[][]>([]);

  // Reverse Pricing States
  const [isReversePricing, setIsReversePricing] = useState(false);
  const [reciprocalValueAndIds, setReciprocalValueAndIds] = useState<
    { value: number; originZoneId: string; destinationZoneId: string }[]
  >([]);

  // Modal Visibility State
  const [isFillEmptyCellsModalVisible, setIsFillEmptyCellsModalVisible] = useState(false);
  const [isAdjustAllCellsModalVisible, setIsAdjustAllCellsModalVisible] = useState(false);

  // Modal Input State
  const [fillEmptyCellsValue, setFillEmptyCellsValue] = useState(0);
  const [adjustAllCellsValue, setAdjustAllCellsValue] = useState(0);
  const [adjustAllCellsType, setAdjustAllCellsType] = useState<IAdjustCellsType['type']>('fixed');

  const [fillEmptyCellsForm] = Form.useForm();
  const [adjustAllValuesForm] = Form.useForm();

  const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined);

  const { data: currentAssignedZoneTable, refetch: refetchCurrentZoneTable } =
    priceSetHook.useEntity<BasePriceByZoneDTO>(`${priceSetId}/zone`, { retry: 1 });

  const zoneTableValuesReversMapper = useCallback(
    (zoneTableValues: IZoneLookupRow[]): ZoneTableValues[] => {
      return (
        zoneTableValues?.map((table) => ({
          originZoneId: table.fromId,
          destinationZoneId: table.toId,
          value: table.value as number,
        })) || []
      );
    },
    []
  );

  const zoneTableValueMapper = useCallback(
    (zoneTableValues: BasePriceByZoneDTO): IZoneLookupRow[] => {
      return (
        zoneTableValues?.zoneTableValues
          ?.map((table) => ({
            fromId: table.originZoneId,
            toId: table.destinationZoneId,
            value: table.value,
          }))
          .filter((cell: any) => cell.value && !isNaN(cell.value as number)) || []
      );
    },
    []
  );

  useEffect(() => {
    if (currentAssignedZoneTable && currentAssignedZoneTable?.zoneTableValues?.length > 0) {
      const mappedZoneTableValues: IZoneLookupRow[] =
        zoneTableValueMapper(currentAssignedZoneTable);
      setCellValues(mappedZoneTableValues);
    }
  }, [currentAssignedZoneTable, zoneTableValueMapper]);

  const navigate = useNavigate();

  const isChanged = useMemo(
    () =>
      JSON.stringify(cellValues) !==
      (currentAssignedZoneTable && JSON.stringify(zoneTableValueMapper(currentAssignedZoneTable))),
    [cellValues, currentAssignedZoneTable, zoneTableValueMapper]
  );

  const onSubmit = async () => {
    try {
      if (currentAssignedZoneTable?.zoneTableValues && isChanged) {
        customAlert.warning({
          title: t('priceSetPage.alert.overWriteZoneTableOnSave'),
          message: t('priceSetPage.alert.updateZoneTableConfirmationMsg'),
          firstButtonTitle: t('common.doItAnyWay'),
          secondButtonTitle: t('common.cancel'),
          firstButtonFunction: async () => {
            await priceSetService.update(`${priceSetId}/zone`, {
              name: 'untitled', // TODO: remove this name property after discussion
              zoneTableValues: zoneTableValuesReversMapper(cellValues),
            });
            await refetchCurrentZoneTable();
            setIsBlocked(false);
            notificationManager.success({
              message: `${t('common.success')}`,
              description: t('priceSetPage.notification.successAssignedZoneTable'),
            });
            customAlert.destroy();
          },
          secondButtonFunction: () => {
            customAlert.destroy();
          },
        });
        return;
      }
      await priceSetService.update(`${priceSetId}/zone`, {
        name: 'untitled', // TODO: remove this name properly after discussion
        zoneTableValues: zoneTableValuesReversMapper(cellValues),
      });
      await refetchCurrentZoneTable();
      setIsBlocked(false);
      notificationManager.success({
        message: `${t('common.success')}`,
        description: t('priceSetPage.notification.successAssignedZoneTable'),
      });
      customAlert.destroy();
    } catch (error) {
      notificationManager.error({
        message: `${t('common.error')}`,
        description: t('priceSetPage.notification.errorAssignZoneTable'),
      });
      logger.error('ERROR', error as Error);
    }
  };

  useEffect(() => {
    const removedNullValues = cellValues.filter(
      (cell) => cell.value && !isNaN(cell.value as number)
    );

    const isTableValueChanged =
      JSON.stringify(removedNullValues) !==
      (currentAssignedZoneTable && JSON.stringify(zoneTableValueMapper(currentAssignedZoneTable)));

    setIsBlocked(isTableValueChanged);
  }, [cellValues, zoneTables, currentAssignedZoneTable]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!activeCell) return;

    const { row, col } = activeCell;
    let newRow = row;
    let newCol = col;

    switch (event.key) {
      case 'ArrowUp':
        newRow = Math.max(row - 1, 0);
        break;
      case 'ArrowDown':
        newRow = Math.min(row + 1, zones ? zones?.data?.length - 1 : 0);
        break;
      case 'ArrowLeft':
        newCol = Math.max(col - 1, 0);
        break;
      case 'ArrowRight':
        newCol = Math.min(col + 1, zones ? zones?.data?.length - 1 : 0);
        break;
      case 'Escape':
        setActiveCell(null);
        setHighlight(t('zonePage.zoneLookUp.hoverOverACell'));
        return;
      default:
        return;
    }

    if (isReversePricing) {
      handleReciprocal();
    }

    setActiveCell({ row: newRow, col: newCol });
    setHighlight(
      `From: ${zones && zones?.data[newRow]?.name} → To: ${zones && zones?.data[newCol]?.name}`
    );
  };

  const handleCellClick = (rowIndex: number, colIndex: number): void => {
    setActiveCell({ row: rowIndex, col: colIndex });
    if (isReversePricing) {
      handleReciprocal();
    }
  };

  const captureState = useCallback(() => {
    setUndoStack((prev) => [...prev, cellValues]);
    setRedoStack([]); // Clear redo stack when a new action is performed
  }, [cellValues]);

  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;
    const previousState = undoStack[undoStack.length - 1];
    setUndoStack((prev) => prev.slice(0, -1));
    setRedoStack((prev) => [...prev, cellValues]);
    setCellValues(previousState);
  }, [cellValues, undoStack]);

  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;
    const nextState = redoStack[redoStack.length - 1];
    setRedoStack((prev) => prev.slice(0, -1));
    setUndoStack((prev) => [...prev, cellValues]);
    setCellValues(nextState);
  }, [cellValues, redoStack]);

  useEffect(() => {
    const handleShortcuts = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'z') {
        event.preventDefault();
        handleUndo();
      }
      if (event.ctrlKey && event.key === 'y') {
        event.preventDefault();
        handleRedo();
      }
    };

    document.addEventListener('keydown', handleShortcuts);
    return () => document.removeEventListener('keydown', handleShortcuts);
  }, [handleUndo, handleRedo]);

  const { setIsBlocked } = useNavigationContext();

  const handleInputChange = (rowIndex: string, colIndex: string, value: string): void => {
    captureState();

    const updateCells = (newValue: number | null) => {
      setCellValues((prev) => {
        const existingIndex = prev.findIndex(
          (cell) => cell.fromId === rowIndex && cell.toId === colIndex
        );

        const updatedCells =
          existingIndex !== -1
            ? prev.map((cell, idx) => (idx === existingIndex ? { ...cell, value: newValue } : cell))
            : [...prev, { fromId: rowIndex, toId: colIndex, value: newValue }];

        return updatedCells;
      });
    };

    if (value.length === 0) {
      updateCells(null);

      if (isReversePricing) {
        setReciprocalValueAndIds((prev) =>
          prev.filter(
            (cell) =>
              !(
                (cell.originZoneId === rowIndex && cell.destinationZoneId === colIndex) ||
                (cell.originZoneId === colIndex && cell.destinationZoneId === rowIndex)
              )
          )
        );
      }
      return;
    }

    const parsedValue = parseFloat(value);
    if (!isNaN(parsedValue)) {
      if (isReversePricing) {
        setReciprocalValueAndIds([
          ...reciprocalValueAndIds,
          { value: parsedValue, originZoneId: rowIndex, destinationZoneId: colIndex },
        ]);
      }

      updateCells(parsedValue);
      return;
    }
  };

  const isCellValueEmpty = useCallback((value: number | null | undefined | string): boolean => {
    return value === null || value === undefined || value === '';
  }, []);

  const handleFillEmptyCells = () => {
    captureState();
    if (zones?.data && zones?.data?.length > 0) {
      const totalCells = zones.data.length * zones.data.length;
      const filledCells = cellValues.filter((cell) => !isCellValueEmpty(cell.value)).length;
      if (filledCells === totalCells) {
        notificationManager.error({
          message: t('zonePage.zoneLookUp.valueCannotBeFilled'),
          description: t('zonePage.zoneLookUp.allCellsAlreadyFilled'),
        });
        return;
      }
      const newCellValues = [...cellValues];

      zones.data.forEach((row) => {
        zones.data.forEach((col) => {
          const existingCellIndex = newCellValues.findIndex(
            (cell) => cell.fromId === `${row.id}` && cell.toId === `${col.id}`
          );

          if (existingCellIndex === -1) {
            newCellValues.push({
              fromId: `${row.id}`,
              toId: `${col.id}`,
              value: fillEmptyCellsValue,
            });
          } else if (isCellValueEmpty(newCellValues[existingCellIndex].value)) {
            newCellValues[existingCellIndex] = {
              ...newCellValues[existingCellIndex],
              value: fillEmptyCellsValue,
            };
          }
        });
      });

      setCellValues(newCellValues);
      setIsFillEmptyCellsModalVisible(false);
      setFillEmptyCellsValue(0);
      fillEmptyCellsForm.resetFields();

      notificationManager.success({
        message: `${t('common.success')}`,
        description: `${t('zonePage.zoneLookUp.emptyCellsFilled')}`,
      });
    }
  };

  const handleOnCloseForEmptyCells = () => {
    setFillEmptyCellsValue(0);
    fillEmptyCellsForm.resetFields();
    setIsFillEmptyCellsModalVisible(false);
  };

  const handleReciprocal = useCallback(() => {
    captureState();
    const newCellValues = [...cellValues];

    if (reciprocalValueAndIds.length > 0) {
      reciprocalValueAndIds.forEach((cellForParent) => {
        const existingIndex = newCellValues.findIndex(
          (cell) =>
            cell.fromId === cellForParent.destinationZoneId &&
            cell.toId === cellForParent.originZoneId
        );
        if (existingIndex !== -1) {
          newCellValues[existingIndex].value = cellForParent.value;
        } else {
          newCellValues.push({
            fromId: cellForParent.destinationZoneId,
            toId: cellForParent.originZoneId,
            value: cellForParent.value,
          });
        }
      });
    }

    setCellValues(newCellValues);
    setReciprocalValueAndIds([]);
  }, [captureState, cellValues, reciprocalValueAndIds]);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (gridRef.current && !gridRef.current.contains(event.target as Node)) {
        setActiveCell(null);
        if (isReversePricing) {
          handleReciprocal();
        }
        setHighlight(t('zonePage.zoneLookUp.hoverOverACell'));
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [handleReciprocal, isReversePricing, t]);

  const renderFillEmptyCellsModal = () => (
    <CustomModal
      className="!w-[450px] !h-[80%] flex items-center"
      modalTitle={t('zonePage.zoneLookUp.autoFillEmptyCells')}
      modalDescription={t('zonePage.zoneLookUp.fillEmptyCellsWithAmount')}
      maskClosable={false}
      footer={
        <div className="flex gap-2 w-full justify-end">
          <Button
            onClick={handleOnCloseForEmptyCells}
            className="hover:!text-black hover:!border-gray-300"
          >
            {t('common.cancel')}
          </Button>
          <Button
            className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
            onClick={handleFillEmptyCells}
            disabled={!fillEmptyCellsValue}
          >
            {t('zonePage.zoneLookUp.submit')}
          </Button>
        </div>
      }
      open={isFillEmptyCellsModalVisible}
      onCancel={handleOnCloseForEmptyCells}
    >
      <div className="space-y-2 p-y-3">
        <div className="flex gap-1">
          <Text className="text-black font-[600]">{t('zonePage.zoneLookUp.amount')}</Text>
          <Text className="text-gray-500">(Example 8.75)</Text>
        </div>
        <div>
          <Form form={fillEmptyCellsForm}>
            <Form.Item
              name="emptyCellsFillUp"
              rules={[
                {
                  validator: (_, value) => {
                    if (value > 100000) {
                      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <InputNumber
                value={fillEmptyCellsValue}
                type="number"
                onChange={(value) => setFillEmptyCellsValue(value as number)}
                min={0}
                step={0.01}
                className="w-full border b-primary-600"
                prefix="$"
                placeholder={t('zonePage.zoneLookUp.enterValue')}
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                defaultValue={0.0}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </CustomModal>
  );

  const renderGrid = () => (
    <>
      <div
        className="overflow-auto max-h-[625px] min-h-[400px] border"
        style={{
          borderRadius: '8px 8px 0 0',
          scrollSnapType: 'both mandatory', // Add scroll snapping
        }}
        ref={gridRef}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-label="Zone Lookup Table Grid"
      >
        <div
          className="grid w-max"
          style={{
            gridTemplateColumns: `repeat(${zones ? zones?.data?.length + 1 : 0}, 140px)`,
            scrollSnapAlign: 'start', // Ensure cells snap
          }}
        >
          {/* Top-left corner */}
          <div className="sticky top-0 left-0 bg-primary-50 font-bold text-center border z-10 p-2">
            {t('zonePage.zoneLookUp.zones')}
          </div>

          {/* Column Headers */}
          {zones?.data?.map((zone, colIndex) => (
            <div
              key={`col-header-${colIndex}`}
              className={`sticky top-0 bg-primary-50 font-bold text-center border p-2 ${
                activeCell?.col === colIndex ? 'bg-blue-100 font-extrabold' : ''
              }`}
              role="columnheader"
            >
              {zone.name}
            </div>
          ))}

          {/* Rows */}
          {zones?.data?.map((rowZone, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {/* Row Header */}
              <div
                className={`sticky left-0 font-bold text-center border z-10 p-2 ${
                  rowIndex % 2 === 0 ? 'bg-primary-25' : 'bg-white'
                }  ${activeCell?.row === rowIndex ? 'bg-blue-100 font-extrabold' : ''}`}
                role="rowheader"
              >
                {rowZone.name}
              </div>

              {zones?.data?.map((colZone, colIndex) => {
                // Find the cell value for this specific fromId and toId
                const cellValue = cellValues.find(
                  (cell) => cell.fromId === `${rowZone.id}` && cell.toId === `${colZone.id}`
                );

                const isActive = activeCell?.row === rowIndex && activeCell?.col === colIndex;

                return (
                  <div
                    key={`${rowIndex}-${colIndex}`}
                    className={`border p-2 text-center scroll-snap-align-start hover:bg-blue-100 cursor-pointer  
                  ${
                    isActive
                      ? 'bg-blue-200'
                      : activeCell?.col === colIndex || activeCell?.row === rowIndex
                        ? 'bg-blue-50'
                        : 'bg-white'
                  }
                  `}
                    onClick={() => handleCellClick(rowIndex, colIndex)}
                    onMouseEnter={() => setHighlight(`From: ${rowZone.name} → To: ${colZone.name}`)}
                    onMouseLeave={() => setHighlight(t('zonePage.zoneLookUp.hoverOverACell'))}
                    role="gridcell"
                    aria-selected={isActive}
                  >
                    {isActive ? (
                      <input
                        defaultValue={
                          cellValue?.value !== null && cellValue?.value !== undefined
                            ? cellValue.value.toString()
                            : ''
                        }
                        maxLength={8}
                        onInput={(e) => {
                          e.currentTarget.value = e.currentTarget.value.replace(/[^0-9.]/g, '');
                        }}
                        onChange={(e) =>
                          handleInputChange(`${rowZone.id}`, `${colZone.id}`, e.target.value)
                        }
                        className="w-full text-center border border-gray-300 rounded focus:outline-none focus:ring focus:ring-blue-300"
                        autoFocus
                        placeholder={t('zonePage.zoneLookUp.enterValue')}
                      />
                    ) : (
                      <span
                        className={`text-sm ${cellValue?.value ? 'text-black' : 'text-gray-400 italic'}`}
                      >
                        {cellValue?.value || 'N/A'}
                      </span>
                    )}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
      <div
        className="sticky top-0 border bg-primary-50 p-2"
        style={{ borderRadius: '0px 0px 8px 8px' }}
      >
        {highlight}
      </div>
    </>
  );

  const zoneTablesOption = useMemo(() => {
    return zoneTables?.data?.map((table) => {
      return { value: table.id as string, label: table.name };
    });
  }, [zoneTables]);

  const getZoneTableById = async (_: string, option: { label: string; value: string }) => {
    try {
      setSelectedValue(option.value);
      const zoneTable = await zoneTableService.getById<BasePriceByZoneDTO>(`${option.value}`);

      if (currentAssignedZoneTable && currentAssignedZoneTable?.zoneTableValues.length > 0) {
        customAlert.warning({
          title: t('priceSetPage.alert.overWriteZoneTableConfirmationTitle'),
          message: t('priceSetPage.alert.overWriteZoneTableConfirmationMsg'),
          firstButtonTitle: t('common.doItAnyWay'),
          secondButtonTitle: t('common.cancel'),
          firstButtonFunction: async () => {
            setCellValues(zoneTableValueMapper(zoneTable));
            customAlert.destroy();
          },
          secondButtonFunction: () => {
            setSelectedValue(undefined);
            customAlert.destroy();
          },
        });
        return;
      }

      setCellValues(zoneTableValueMapper(zoneTable));
    } catch (error) {
      logger.error('ERROR', error as Error);
    }
  };

  const handleDiscardChanges = () => {
    if (currentAssignedZoneTable?.zoneTableValues) {
      setCellValues(zoneTableValueMapper(currentAssignedZoneTable));
      setIsBlocked(false);
    }
  };

  const renderButton = () => (
    <div className="flex gap-4">
      <Button
        type="primary"
        onClick={onSubmit}
        className="h-[40px] border px-6 rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
        disabled={cellValues.length <= 0 || !isChanged}
      >
        {t('common.save')}
      </Button>
    </div>
  );

  const handleOnCloseForAdjustAllCells = () => {
    setIsAdjustAllCellsModalVisible(false);
    setAdjustAllCellsValue(0);
    adjustAllValuesForm.resetFields();
  };

  const renderAdjustAllCellsModal = () => (
    <CustomModal
      modalTitle={t('zonePage.zoneLookUp.bulkAdjustModalTitle')}
      modalDescription={t('zonePage.zoneLookUp.adjustPopulatedCellsByAmount')}
      open={isAdjustAllCellsModalVisible}
      onCancel={handleOnCloseForAdjustAllCells}
      maskClosable={false}
      className="!w-[450px] !h-[80%] flex items-center"
      footer={
        <div className="flex gap-2 w-full justify-end">
          <Button
            onClick={handleOnCloseForAdjustAllCells}
            className="hover:!text-black hover:!border-gray-300"
          >
            {t('common.cancel')}
          </Button>
          <Button
            className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
            onClick={handleAdjustAllCells}
            disabled={!adjustAllCellsValue}
          >
            {t('zonePage.zoneLookUp.submit')}
          </Button>
        </div>
      }
      width={450} // Set modal width
    >
      <div className="space-y-6">
        <div>
          <Text className="block mb-2 text-sm font-medium text-gray-700">
            {t('zonePage.zoneLookUp.adjustmentAmountLabel')}
          </Text>
          <div className="flex justify-between items-center py-2 w-full">
            <div className="fixed-radio flex gap-2 items-center bg-primary-25 w-[46%] rounded-lg p-2">
              <input
                className=""
                type="radio"
                checked={adjustAllCellsType === 'fixed'}
                onChange={() => {
                  setAdjustAllCellsType('fixed');
                  adjustAllValuesForm.resetFields();
                }}
              />
              <label className="mr-2 font-semibold text-gray-700">
                {t('zonePage.zoneLookUp.treatAsFixedLabel')}
              </label>
            </div>
            <div className="percentage-radio flex gap-2 items-center bg-primary-25 w-[50%] rounded-lg p-2">
              <input
                type="radio"
                checked={adjustAllCellsType === 'percentage'}
                onChange={() => {
                  setAdjustAllCellsType('percentage');
                  adjustAllValuesForm.resetFields();
                }}
              />
              <label className="font-semibold text-gray-700">
                {t('zonePage.zoneLookUp.treatAsPercentageLabel')}
              </label>
            </div>
          </div>

          <div className="flex gap-1 px-1 py-1">
            <Text className="text-black font-[600]">{t('zonePage.zoneLookUp.amount')}</Text>
            <Text className="text-gray-500">(Example 8.75)</Text>
          </div>

          <Form form={adjustAllValuesForm}>
            <Form.Item
              rules={[
                {
                  validator: (_, value) => {
                    if (adjustAllCellsType === 'fixed') {
                      if (value > 100000) {
                        return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                      }
                    } else {
                      if (value > 100) {
                        return Promise.reject(
                          new Error(t('priceModifiers.maximumPercentExceeded'))
                        );
                      }
                    }

                    return Promise.resolve();
                  },
                },
              ]}
              name="adjustAllValues"
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                addonBefore={adjustAllCellsType === 'fixed' ? '$' : '%'}
                name="Amount"
                value={adjustAllCellsValue}
                onChange={(value) => setAdjustAllCellsValue(value ?? 0)}
                className="bulk-adjust-input w-full p-1"
                placeholder="0.00"
                parser={(value) => {
                  if (!value) {
                    return 0;
                  }
                  const parsedValue = parseFloat(value.replace(/\$\s?|(,*)/g, ''));
                  return isNaN(parsedValue) ? 0 : parsedValue;
                }}
                min={0}
                step={0.01}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </CustomModal>
  );

  const handleAdjustAllCells = () => {
    captureState();
    const cellsWithValues = cellValues.filter((cell) => !isCellValueEmpty(cell.value));

    if (cellsWithValues.length === 0) {
      notificationManager.error({
        message: t('zonePage.zoneLookUp.valueCannotBeAdjusted'),
        description: t('zonePage.zoneLookUp.allCellsEmpty'),
      });
      return;
    }
    const adjustValue = adjustAllCellsValue;

    const newCellValues = cellValues.map((cell) => {
      const currentValue = parseFloat(String(cell.value));

      if (isCellValueEmpty(currentValue)) {
        return cell;
      }

      const numericValue = currentValue as number;
      if (adjustAllCellsType === 'fixed') {
        return {
          ...cell,
          value: Number((numericValue + adjustValue).toFixed(2)),
        };
      } else {
        return {
          ...cell,
          value: Number((numericValue * (1 + adjustValue / 100)).toFixed(2)),
        };
      }
    });

    setCellValues(newCellValues);
    setIsAdjustAllCellsModalVisible(false);
    setAdjustAllCellsValue(0);
    adjustAllValuesForm.resetFields();
    notificationManager.success({
      message: `${t('common.success')}`,
      description: `${t('zonePage.zoneLookUp.allCellsAdjusted')}`,
    });
  };

  return (
    <div className="h-full pr-6 py-5">
      {renderFillEmptyCellsModal()}
      {renderAdjustAllCellsModal()}
      <header className="flex justify-end items-center gap-4">
        <Button
          onClick={() => setIsReversePricing(!isReversePricing)}
          className="p-5 px-4 hover:!text-black hover:!border-gray-300"
        >
          <Checkbox checked={isReversePricing} />
          {t('zonePage.zoneLookUp.reversePricing')}
        </Button>

        <Button
          className="p-5 px-4 hover:!text-black hover:!border-gray-300"
          icon={<EditOutlined />}
          onClick={() => setIsFillEmptyCellsModalVisible(true)}
        >
          {t('zonePage.zoneLookUp.autoFillEmptyCellsButton')}
        </Button>

        <Button
          className="p-5 px-4 hover:!text-black hover:!border-gray-300"
          onClick={() => setIsAdjustAllCellsModalVisible(true)}
        >
          <img src={ArrowSvgIcon} />
          {t('zonePage.zoneLookUp.bulkAdjustGridValue')}
        </Button>

        <Select
          options={zoneTablesOption}
          prefixCls="custom-select"
          loading={isFetching || isLoading}
          className="w-full max-w-[400px] md:w-full"
          suffixIcon={<SelectDownArrow />}
          placeholder={t('priceSetPage.form.selectZoneTableGridPlaceholder')}
          onSelect={getZoneTableById}
          value={selectedValue}
        />
      </header>
      <main
        className={`py-4 min-h-[700px] ${(!zones || zones?.data?.length <= 0) && 'flex justify-center items-center'}`}
      >
        {zones ? (
          zones?.data?.length > 0 ? (
            renderGrid()
          ) : (
            <EmptyStatePage
              title={t('zonePage.noZonesFound')}
              description={t('zonePage.createNewZone')}
              link={t('zonePage.addZone')}
              onLinkAction={() => navigate(ROUTES.LOCATION.LOCATION_ZONE)}
            />
          )
        ) : (
          <Spinner />
        )}
        <div className="py-4 flex gap-4">
          {cellValues.length <= 0 || !isChanged ? (
            <CustomTooltip title={t('priceSetPage.tooltip.disableZoneTableSaveBtn')}>
              {zones && zones?.data?.length > 0 && renderButton()}
            </CustomTooltip>
          ) : (
            renderButton()
          )}
          <Button
            disabled={!isChanged}
            className="min-w-[155px] h-[40px] border-[1px] rounded-[8px] max-w-fit"
            onClick={handleDiscardChanges}
          >
            {t('zonePage.zoneLookUp.discardChanges')}
          </Button>
        </div>
      </main>
    </div>
  );
};

export default BasePriceByZone;
