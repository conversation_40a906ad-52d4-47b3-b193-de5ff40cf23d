import { CreatePriceSetDto } from '@/api/priceSet/priceSet.types';
import { priceSetHook } from '@/api/priceSet/usePriceSet';
import { infoCircleOutlined } from '@/assets';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { formErrorRegex } from '@/constant/Regex';
import { useLanguage } from '@/hooks/useLanguage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { isFormChangedHandler } from '@/lib/helper';
import { logger } from '@/lib/logger/logger';
import { Button, Form, Input, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import React, { useCallback, useEffect } from 'react';
import { useParams } from 'react-router-dom';

const PaymentOptions = [
  {
    value: 'none',
    label: 'None',
  },
  // {
  //   value: 'partial',
  //   label: 'Authorized amount',
  // },
  // {
  //   value: 'full',
  //   label: 'Full',
  // },
];

export interface IPriceSetGeneralProps {
  onFinish: (values: CreatePriceSetDto) => Promise<void>;
}

const PriceSetGeneral: React.FC<IPriceSetGeneralProps> = (props) => {
  const { id: priceSetId } = useParams();

  const { onFinish } = props;
  const isEditMode = Boolean(priceSetId);

  const { data: priceSetData } = priceSetHook.useEntity(priceSetId as string, {
    enabled: isEditMode,
  });
  const { t } = useLanguage();

  const [priceSetForm] = useForm();
  const { setIsBlocked } = useNavigationContext();

  const onFieldsChangeHandler = (changesFields: any) => {
    if (changesFields.length <= 1 && priceSetData) {
      const isIsChange = isFormChangedHandler(priceSetData, priceSetForm.getFieldsValue(true));
      setIsBlocked(isIsChange);
    } else if (changesFields.length > 1) {
      setIsBlocked(false);
    }
  };

  const getCurrentPriceSet = useCallback(async () => {
    try {
      if (priceSetId && priceSetData) {
        priceSetForm.setFieldsValue(priceSetData);
      }
    } catch (error) {
      logger.error('ERROR', error as Error);
    }
  }, [priceSetData, priceSetForm, priceSetId]);

  useEffect(() => {
    if (priceSetId) {
      getCurrentPriceSet();
    }
  }, [getCurrentPriceSet, priceSetId]);

  return (
    <div className="h-full pr-6">
      <CustomDivider label={t('common.divider.basicDetails')} className="py-4" />
      <Form
        name="priceSet"
        className="custom-form"
        layout="vertical"
        form={priceSetForm}
        onFinish={onFinish}
        onFieldsChange={onFieldsChangeHandler}
      >
        <div className="flex flex-col gap-2.5 md:gap-4">
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-3 3xl:grid-cols-3">
            <Form.Item
              label={t('priceSetPage.form.name')}
              validateFirst
              name="name"
              rules={[
                { required: true, message: t('priceSetPage.form.serviceNameRequired') },
                { whitespace: true, message: t('priceSetPage.form.serviceNameRequired') },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
              ]}
            >
              <Input placeholder={t('priceSetPage.form.namePlaceholder')} maxLength={255} />
            </Form.Item>
            <Form.Item
              label={t('priceSetPage.form.serviceLevel')}
              name="internalName"
              validateFirst
              rules={[
                { required: true, message: t('priceSetPage.form.serviceLevelRequired') },
                { whitespace: true, message: t('priceSetPage.form.serviceLevelRequired') },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
              ]}
            >
              <Input placeholder={t('priceSetPage.form.serviceLevelPlaceholder')} maxLength={255} />
            </Form.Item>
            <Form.Item
              label={t('priceSetPage.form.paymentOption')}
              name="paymentOption"
              initialValue={PaymentOptions[0].value}
            >
              <Select
                options={PaymentOptions}
                placeholder={t('priceSetPage.form.paymentOptionPlaceholder')}
                prefixCls="custom-select"
                suffixIcon={<SelectDownArrow />}
              />
            </Form.Item>
          </div>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Form.Item
              label={
                <>
                  <CustomTooltip title={t('priceSetPage.tooltip.notes')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                  {t('priceSetPage.form.notes')}
                </>
              }
              name="notes"
            >
              <TextArea
                placeholder={t('priceSetPage.form.notesPlaceholder')}
                maxLength={255}
                className="large-textarea"
              />
            </Form.Item>
            <Form.Item
              label={
                <>
                  <CustomTooltip title={t('priceSetPage.tooltip.description')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                  {t('priceSetPage.form.description')}
                </>
              }
              name="description"
            >
              <TextArea
                placeholder={t('priceSetPage.form.descriptionPlaceHolder')}
                maxLength={255}
                className="large-textarea"
              />
            </Form.Item>
          </div>
        </div>
        <Button
          htmlType="submit"
          type="primary"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600 px-6 h-[40px] mt-5"
        >
          {priceSetId ? t('common.update') : t('common.save')}
        </Button>
      </Form>
    </div>
  );
};

export default PriceSetGeneral;
