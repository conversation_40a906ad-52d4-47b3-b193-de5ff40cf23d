import { useMemo, useState } from 'react';
import AssignedCustomersGrid from './AssignedCustomersGrid';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import {
  ICustomerService,
  IServiceSearchHighlight,
} from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { ICellRendererParams } from 'ag-grid-community';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import { useParams } from 'react-router-dom';
import { ICustomer } from '@/api/customer/customer.types';
import {
  assignedCustomersHook,
  assignedCustomersService,
} from '@/api/priceSet/assignedCustomers/useAssignedCustomers';
import { customerHook } from '@/api/customer/useCustomer';
import { IGetAssignedCustomers } from '@/api/priceSet/assignedCustomers/assignedCustomers.types';
import notificationManagerInstance from '@/hooks/useNotificationManger';

const PriceSetCustomersComponent = () => {
  const [isEdit, setIsEdit] = useState(false);
  const [searchText, setSearchText] = useState<IServiceSearchHighlight>({
    searchTextForAvailable: '',
    searchTextForSelected: '',
    searchTextForAssigned: '',
  });
  const { id: priceSetId } = useParams();
  const { t } = useLanguage();

  const { data: assignedCustomers, refetch: refetchAssignedCustomers } =
    assignedCustomersHook.useEntities(`${priceSetId}/customers`);

  const { data: customers } = customerHook.useEntities('all/minimal');

  const unAssignedCustomers = useMemo(() => {
    return customers?.data.filter(
      (service) =>
        !assignedCustomers?.data.some((assignedService) => {
          return assignedService.customerId === service.id;
        })
    );
  }, [assignedCustomers, customers]);

  const assignedCustomerMasked = useMemo(() => {
    return assignedCustomers?.data.map((service) => ({ ...service, id: service.customerId }));
  }, [assignedCustomers]) as IGetAssignedCustomers[];

  const customerServiceColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'companyName',
        headerName: t('priceSetPage.customers.colDefs.companyName'),
        unSortIcon: true,
        type: 'string',
        visible: true,
        flex: 1,
        maxWidth: 900,
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          if (params.context) {
            const isAvailableGrid = params.context.name === 'available';
            const relevantSearchText = isAvailableGrid
              ? searchText.searchTextForAvailable
              : searchText.searchTextForSelected;

            return (
              (relevantSearchText && highlightText(params.value, relevantSearchText)) ||
              params.value
            );
          }
          return searchText.searchTextForAssigned
            ? highlightText(params.value, searchText.searchTextForAssigned)
            : params.value;
        },
      },
      {
        field: 'contactName',
        headerName: t('priceSetPage.customers.colDefs.customerName'),
        unSortIcon: true,
        visible: true,
        flex: 1,
        maxWidth: 900,
        type: 'string',
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          if (params.context) {
            const isAvailableGrid = params.context.name === 'available';
            const relevantSearchText = isAvailableGrid
              ? searchText.searchTextForAvailable
              : searchText.searchTextForSelected;

            return (
              (relevantSearchText && highlightText(params.value, relevantSearchText)) ||
              params.value
            );
          }
          return searchText.searchTextForAssigned
            ? highlightText(params.value, searchText.searchTextForAssigned)
            : params.value;
        },
      },
    ];
  }, [
    searchText.searchTextForAssigned,
    searchText.searchTextForAvailable,
    searchText.searchTextForSelected,
    t,
  ]);

  const onSave = async (selectedData: ICustomerService[]) => {
    const customerIds = selectedData.map((data) => data.id);
    try {
      await assignedCustomersService.update(`${priceSetId}/customers`, { customerIds });
      await refetchAssignedCustomers();
    } catch (error) {
      notificationManagerInstance.error({
        message: t('common.error'),
        description: t('systemErrors.whileAssigningServices'),
      });
    }
  };

  return (
    <>
      {isEdit ? (
        <TransferGrid<ICustomer>
          setIsEdit={setIsEdit}
          colDefs={customerServiceColDefs}
          initialRowData={unAssignedCustomers || []}
          setSearchText={setSearchText}
          assignedServices={assignedCustomerMasked}
          searchText={searchText}
          hideBackNavigation
          onSave={onSave}
          mainHeaderTitle={t('priceSetPage.customers.assignCustomersTitle')}
          availableGridHeader={t('priceSetPage.customers.unassignedCustomers')}
          availableGridSearchPlaceholder={t('priceSetPage.customers.searchPlaceholder')}
          selectedGridHeader={t('priceSetPage.customers.assignedCustomers')}
          selectedGridSearchPlaceholder={t('priceSetPage.customers.searchPlaceholder')}
          availableGridEmptyStateTitle={t('priceSetPage.customers.noCustomersAvailable')}
          availableGridEmptyStateDescription={t(
            'priceSetPage.customers.noCustomersAvailableToAssign'
          )}
          selectedGridEmptyState={t('priceSetPage.customers.noCustomersAssigned')}
          navigationAndRefreshBlocker
          isDataRequired={false}
        />
      ) : (
        <AssignedCustomersGrid
          setIsEdit={setIsEdit}
          colDefs={customerServiceColDefs}
          allCustomers={assignedCustomerMasked}
          noSCustomersAvailableInSystem={customers?.data.length === 0}
          searchText={searchText}
          setSearchText={setSearchText}
        />
      )}
    </>
  );
};
export default PriceSetCustomersComponent;
