import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { Button, Form, Input, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useMemo, useState } from 'react';
import SavedGroupModifiersList from './SavedGroupModifiersList';
import { useNavigate, useParams } from 'react-router-dom';
import '../../modifiers.css';
import { formErrorRegex } from '@/constant/Regex';
import { useLanguage } from '@/hooks/useLanguage';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { ROUTES } from '@/constant/RoutesConstant';
import { priceModifierHook } from '@/api/priceModifier/usePriceModifier';
import { IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';
import { groupModifierHook } from '@/api/groupModifiers/useGroupModifier';
import { defaultPagination } from '@/constant/generalConstant';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { isFormChangedHandler } from '@/lib/helper';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { ModifierGridIcon, ModifierGroupGridIcon } from '@/assets';
import { IServiceSearchHighlight } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { IColDef, TGridProps } from '@/types/AgGridTypes';
import { GridIdConstant } from '@/constant/GridIdConstant';

const PricesGroupModifiers = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useLanguage();
  const [isEdit, setIsEdit] = useState(false);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState(defaultPagination);
  const { data: priceModifiersList } = priceModifierHook.useEntities('combined', pagination);

  const { data: getSelectedGroupModifier } = groupModifierHook.useEntity(id as string);
  const [navigationBlocker, setNavigationBlocker] = useState({ form: false, grid: false });
  const [selectedData, setSelectedData] = useState<IPriceModifiersListing[]>([]);

  const [searchText, setSearchText] = useState<IServiceSearchHighlight>({
    searchTextForAvailable: '',
    searchTextForSelected: '',
    searchTextForAssigned: '',
  });

  const notificationManager = useNotificationManager();
  const behaviorOptions = [
    {
      value: 'UseHighest',
      label: t('priceModifiers.groupModifiers.useHighestPricedModifier'),
    },
    { value: 'UseLowest', label: t('priceModifiers.groupModifiers.useLowestPricedModifier') },
    { value: 'UseSum', label: t('priceModifiers.groupModifiers.useSumOfAllModifiers') },
  ];
  const navigate = useNavigate();

  const assignedModifiersMasked = useMemo(() => {
    const members = getSelectedGroupModifier?.members || [];
    const combined = [...members, ...selectedData];
    return removeDuplicates(combined, 'id');
  }, [getSelectedGroupModifier?.members, selectedData]);

  const unAssignedModifiers = useMemo(() => {
    const assignedIds = new Set(assignedModifiersMasked.map((m) => m.id));
    return (priceModifiersList?.data || []).filter(
      (modifier) => !assignedIds.has(modifier.id) && modifier.id !== id
    );
  }, [assignedModifiersMasked, id, priceModifiersList?.data]);

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      form.setFieldsValue(getSelectedGroupModifier);
      if (!getSelectedGroupModifier?.members) return;
      setSelectedData(getSelectedGroupModifier?.members as IPriceModifiersListing[]);
    }
  }, [form, getSelectedGroupModifier, id]);

  const createGroupModifierMutation = groupModifierHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceModifiers.groupModifiers.groupModifierCreatedSuccessfully'),
      });
      setIsEdit(false);
      setNavigationBlocker({ form: false, grid: false });
    },
  });

  const updateGroupModifierMutation = groupModifierHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceModifiers.groupModifiers.groupModifierUpdatedSuccessfully'),
      });
      setNavigationBlocker({ form: false, grid: false });
    },
  });
  const handleSave = async () => {
    try {
      if (selectedData.length === 0) {
        notificationManager.error({
          message: t('common.error'),
          description: t('zonePage.zoneLookUp.pleaseAddModifiersToGroup'),
        });
        return;
      }
      const values = await form.validateFields();
      const formValues = form.getFieldsValue();
      const formattedData = {
        name: values.name,
        behavior: values.behavior,
        description: values.description,
        members: selectedData.map((item) => {
          return { id: item.id, isGroup: item.isGroup };
        }),
      };
      if (!id) {
        const newGroupModifier = await createGroupModifierMutation.mutateAsync(
          formattedData as any
        );
        navigate(
          ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_EDIT.replace(
            ':id',
            newGroupModifier.id as string
          ).replace(':tab', 'general'),
          { replace: true }
        );
      } else {
        await updateGroupModifierMutation.mutateAsync({
          id: id,
          data: {
            ...formValues,
            members: selectedData.map((item) => {
              return { id: item.id, isGroup: item.isGroup };
            }),
          },
        });
      }
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const priceModifierGroupColDefs: IColDef[] = useMemo(
    () => [
      {
        headerName: t('zonePage.colDefs.name'),
        key: 'Name',
        unSortIcon: true,
        type: 'string',
        flex: 1,
        field: 'name',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          const checkboxId = `${params.data?.id}`;

          const isAvailableGrid = params.context.name === 'available';

          const relevantSearchText = isAvailableGrid
            ? searchText.searchTextForAvailable
            : searchText.searchTextForSelected;

          return (
            <div className="flex gap-2">
              {params?.data?.isGroup ? (
                <div className="flex gap-1 items-center">
                  <ModifierGroupGridIcon />
                  {relevantSearchText ? (
                    (highlightText(params?.data?.name, relevantSearchText) ?? (
                      <label htmlFor={checkboxId}>{params.data?.name}</label>
                    ))
                  ) : (
                    <label htmlFor={checkboxId}>{params.data?.name}</label>
                  )}
                </div>
              ) : (
                <div className="flex gap-1 items-center">
                  <ModifierGridIcon />
                  {relevantSearchText ? (
                    (highlightText(params.data?.name, relevantSearchText) ?? (
                      <label htmlFor={checkboxId}>{params.data?.name}</label>
                    ))
                  ) : (
                    <label htmlFor={checkboxId}>{params.data?.name}</label>
                  )}
                </div>
              )}
            </div>
          );
        },
      },
    ],
    [searchText.searchTextForAvailable, searchText.searchTextForSelected, t]
  );

  const onSaveHandler = async (selectedFields: any[]) => {
    setSelectedData(selectedFields);
    setIsEdit(true);
  };

  function removeDuplicates(arr: any[], key: string) {
    const seen = new Set();
    return arr.filter((obj) => {
      const value = obj[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }

  const getNavigationBlockerStateHandler = (isBlocked: boolean) => {
    setNavigationBlocker((prev) => ({ ...prev, grid: id ? isBlocked : false }));
  };

  const { setIsBlocked } = useNavigationContext();

  useEffect(() => {
    setIsBlocked(navigationBlocker.form || navigationBlocker.grid);
  }, [navigationBlocker, setIsBlocked]);

  return (
    <div className="flex flex-col">
      <PageHeadingComponent
        isChildComponent={true}
        title={
          id
            ? t('priceModifiers.groupModifiers.updatePriceModifiersGroup')
            : t('priceModifiers.groupModifiers.addPriceModifiersGroup')
        }
      />
      <Form
        form={form}
        name="priceModifier"
        layout="vertical"
        className="w-full p-5 pb-0 pl-1 flex gap-3"
        onFieldsChange={(changesFields) => {
          if (changesFields.length <= 1) {
            const isIsChange = isFormChangedHandler(
              getSelectedGroupModifier || {},
              form.getFieldsValue(true),
              ['priceModifier']
            );
            setNavigationBlocker((prev) => ({ ...prev, form: isIsChange }));
          } else if (changesFields.length > 1) {
            setNavigationBlocker((prev) => ({ ...prev, form: false }));
          }
        }}
      >
        <div className="flex flex-col w-1/2 justify-between">
          <Form.Item
            required
            rules={[
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                validator: async (_, value) => {
                  if (!value || value.trim() === '') {
                    throw new Error(t('zonePage.operationalForm.nameError'));
                  }
                },
              },
            ]}
            name="name"
            label={t('zonePage.colDefs.name')}
            className="modifier-form-item mb-0"
          >
            <Input
              maxLength={255}
              className="h-[40px]"
              placeholder={t('priceModifiers.groupModifiers.vnpFourHoursFuelCharges')}
            />
          </Form.Item>
          <Form.Item
            rules={[
              {
                required: true,
                message: t('priceModifiers.formulaDescription.pleaseSelectBehavior'),
              },
            ]}
            name="behavior"
            label={t('priceModifiers.groupModifiers.behavior')}
            className="modifier-form-item mb-0"
          >
            <Select
              className="modifier-select-behavior"
              placeholder={t('priceModifiers.groupModifiers.selectBehavior')}
              options={behaviorOptions}
            />
          </Form.Item>
        </div>
        <Form.Item
          className="modifier-form-item w-1/2 mb-0"
          name="description"
          label={t('priceSetPage.form.description')}
        >
          <TextArea
            maxLength={255}
            rows={5}
            placeholder={t('priceModifiers.groupModifiers.descriptionVisibleToAdminsOnly')}
          />
        </Form.Item>
      </Form>
      <div>
        {isEdit ? (
          <div className="flex flex-col gap-3">
            <SavedGroupModifiersList
              setIsEdit={setIsEdit}
              savedData={selectedData}
              setSelectedData={setSelectedData}
              setPagination={setPagination}
            />
            <div>
              <Button
                htmlType="submit"
                type="primary"
                className=" bg-primary-600 hover:!bg-primary-600"
                form="priceModifier"
                onClick={handleSave}
              >
                {t('common.save')}
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col">
            <TransferGrid<IPriceModifiersListing>
              setIsEdit={setIsEdit}
              gridProps={
                {
                  className: '!h-[56vh]',
                  gridId: GridIdConstant.GRID_WRAPPER_FOR_EXTRA_SMALL,
                } as TGridProps
              }
              colDefs={priceModifierGroupColDefs}
              initialRowData={unAssignedModifiers || []}
              assignedServices={assignedModifiersMasked || []}
              setSearchText={setSearchText}
              searchText={searchText}
              onSave={onSaveHandler}
              preventEditModeOnSave
              saveButtonText={t('priceModifiers.configureTiersForm.add')}
              mainHeaderTitle={t('dashboard.customer.services.assignServices')}
              availableGridHeader={t('priceModifiers.groupModifiers.unassignedModifiers')}
              availableGridSearchPlaceholder={t('priceModifiers.groupModifiers.searchModifiers')}
              selectedGridHeader={t('priceModifiers.groupModifiers.assignedModifiersToGroupList')}
              selectedGridSearchPlaceholder={t('priceModifiers.groupModifiers.searchModifiers')}
              availableGridEmptyStateTitle={t('priceModifiers.groupModifiers.noModifiersAvailable')}
              availableGridEmptyStateDescription={t('priceSetPage.priceMod.noModsFoundToAssigned')}
              selectedGridEmptyState={t('priceModifiers.groupModifiers.noModifiersAssigned')}
              hideBackNavigation
              getNavigationBlockerState={getNavigationBlockerStateHandler}
            />
          </div>
        )}
      </div>
    </div>
  );
};
export default PricesGroupModifiers;
