import { FilterBoxDeleteOutlinedIcon, PlusModifierIcon, SelectSuffixIcon } from '@/assets';
import { Button, Form, InputNumber, Select } from 'antd';
import '../../modifiers.css';
import { FormInstance } from 'antd/lib';
import { IConfigureTierFormData, ITierForConfigure } from '../../priceModifiers.types';
import { RangeFromOperator, RangeToOperator } from '../../priceModifiers.types';
import { useLanguage } from '@/hooks/useLanguage';
import { useEffect } from 'react';
import { IPriceModifier } from '@/api/priceModifier/priceModifier.types';
import { useParams } from 'react-router-dom';
interface IConfigureTiersProps {
  configureTiersForm: FormInstance<IConfigureTierFormData>;
  selectedType: string;
  setIsConfigureTierModal: React.Dispatch<React.SetStateAction<boolean>>;
  setConfigureTiersFormData: React.Dispatch<React.SetStateAction<IConfigureTierFormData>>;
  formData: IPriceModifier;
  onTierDataUpdate?: () => void;
  currentTieredData?: IConfigureTierFormData;
}
const ConfigureTieredForm: React.FC<IConfigureTiersProps> = (props) => {
  const { t } = useLanguage();
  const { id } = useParams();

  const {
    configureTiersForm,
    selectedType,
    setConfigureTiersFormData,
    formData,
    setIsConfigureTierModal,
    onTierDataUpdate,
    currentTieredData
  } = props;
  const operatorsForFrom = [
    { label: '>', value: 'GreaterThan' },
    { label: '>=', value: 'GreaterThanOrEqual' },
  ];
  const operatorsForTo = [
    { label: '<', value: 'LessThan' },
    { label: '<=', value: 'LessThanOrEqual' },
  ];
  const isAmount: string = selectedType?.includes('$')
    ? t('priceModifiers.configureTiersForm.amount')
    : t('priceModifiers.configureTiersForm.percentage');
  const validateRanges = (tieredRanges: ITierForConfigure[]): boolean => {
    const indexedRanges = tieredRanges.map((range, index) => ({
      ...range,
      originalIndex: index,
    }));

    const sortedRanges = [...indexedRanges].sort((a, b) => {
      if (a.fromValue !== b.fromValue) {
        return a.fromValue - b.fromValue;
      }
      if (a.toValue === null) return 1;
      if (b.toValue === null) return -1;
      return a.toValue - b.toValue;
    });
    const seen = new Set<string>();
    const uniqueRanges: typeof sortedRanges = [];

    for (const range of sortedRanges) {
      const key = JSON.stringify({
        fromValue: range.fromValue,
        toValue: range.toValue,
        fromOperator: range.fromOperator,
        toOperator: range.toOperator,
        value: range.value
      });
      if (!seen.has(key)) {
        seen.add(key);
        uniqueRanges.push(range);
      }
    }
    for (let i = 1; i < uniqueRanges.length; i++) {
      const prev = uniqueRanges[i - 1];
      const curr = uniqueRanges[i];
      if (prev.toValue === null || prev.toValue === undefined) {
        configureTiersForm.setFields([
          { name: ['tieredRanges', i, 'fromValue'], errors: ['Start value is already covering by previous none value'] },
          { name: ['tieredRanges', i, 'toValue'], errors: ['End value is already covering by previous none value'] }])
        return false
      }

      if (curr.fromValue <= (prev.toValue ?? null)) {
        configureTiersForm.setFields([
          { name: ['tieredRanges', curr.originalIndex, 'fromValue'], errors: [`Start value is already covered by ${prev.fromValue} - ${prev.toValue}`] },
        ])
        return false
      }
    }
    configureTiersForm.setFields([
      { name: ['tieredRanges'], errors: [] }
    ])
    return true
  }
  const handleOnFinish = (e: IConfigureTierFormData) => {
    const isValidateRanges = validateRanges(e?.tieredRanges)
    if (isValidateRanges) {
      const updatedTiers = (e.tieredRanges || []).map(tier => ({
        ...tier,
        fromValue: Number(tier.fromValue),
        toValue: Number(tier.toValue),
        value: Number(tier.value),
      }));
      setConfigureTiersFormData({ ...e, tieredRanges: updatedTiers, tieredDefaultValue: Number(e.tieredDefaultValue) });
      setIsConfigureTierModal(false);
      
      if (onTierDataUpdate) {
        setTimeout(() => {
          onTierDataUpdate();
        }, 100);
      }
    }
  };
  useEffect(() => {
    const dataToUse = currentTieredData;
    
    if (id && dataToUse) {
      if (!dataToUse.tieredRanges || dataToUse.tieredRanges.length === 0) {
        configureTiersForm.setFieldsValue({
          tieredRanges: [{ fromValue: 0, toValue: undefined, fromOperator: RangeFromOperator.GreaterThan, toOperator: RangeToOperator.LessThan }],
          tieredDefaultValue: dataToUse.tieredDefaultValue || 0
        });
      } else {
        const newTiers = (dataToUse.tieredRanges || []).map(tier => ({
          ...tier,
          fromValue: (tier.fromValue === undefined || tier.fromValue === null || typeof tier.fromValue !== 'number' || isNaN(tier.fromValue)) ? 0 : tier.fromValue,
          toValue: (tier.toValue === undefined || tier.toValue === null || typeof tier.toValue !== 'number' || isNaN(tier.toValue)) ? undefined : tier.toValue,
        }));
        configureTiersForm.setFieldsValue({ 
          tieredRanges: newTiers,
          tieredDefaultValue: dataToUse.tieredDefaultValue || 0
        });
      }
    }
  }, [formData, currentTieredData, id, configureTiersForm]);
  return (
    <div>
      <Form
        scrollToFirstError
        form={configureTiersForm}
        onFinish={(e) => handleOnFinish(e)}
        initialValues={{ tieredRanges: [{ fromValue: 0, toValue: undefined }] }}
        autoComplete="off"
        layout="vertical"
        name="configureTiersForm"
      >
        <Form.List name="tieredRanges">
          {(fields, { add, remove }) => (
            <div>
              <Form.Item className="flex justify-end w-[99%] mb-0 h-[50px]">
                <Button
                  className="h-[40px] hover:!border-gray-300 hover:!text-black "
                  icon={<PlusModifierIcon />}
                  onClick={() => add({ fromValue: 0, toValue: undefined })}
                >
                  {t('priceModifiers.configureTiersForm.add')}
                </Button>
              </Form.Item>
              <div className="w-full flex flex-col max-h-[300px] overflow-y-auto">
                {fields?.map((field, index) => (
                  <div className="w-[99%] flex gap-3 items-center">
                    <div className="flex w-full gap-3">
                      <Form.Item
                        dependencies={['tieredRanges', field.name, 'toValue']}
                        {...field}
                        name={[field.name, 'fromValue']}
                        className="required-tag-item mb-3 w-2/6 p-0"
                        label={t('priceModifiers.configureTiersForm.start')}
                        rules={[
                          { required: true, message: t('priceModifiers.formulaDescription.applicableRangeRequired') },
                          {
                            validator: (_, value) => {
                              const to = configureTiersForm.getFieldValue(['tieredRanges', field.name, 'toValue']);
                              if ((value === 0 || value === '0') && (to === 'none' || to === undefined || to === null || to === '')) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.applicableRangeRequired')));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <InputNumber
                          className="input-number-start"
                          onChange={() => {
                            configureTiersForm.validateFields([["tieredRanges", field.name, "toValue"]]);
                          }}
                          min={0}
                          defaultValue={0}
                          addonBefore={
                            <Form.Item
                              initialValue={'GreaterThan'}
                              className="mb-0"
                              name={[field.name, 'fromOperator']}
                            >
                              <Select
                                className="select-item"
                                suffixIcon={<SelectSuffixIcon />}
                                options={operatorsForFrom}
                              />
                            </Form.Item>
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        dependencies={['tieredRanges', field.name, 'fromValue']}
                        name={[field.name, 'toValue']}
                        rules={[
                          {
                            validator: (_, value) => {
                              const from = configureTiersForm.getFieldValue(['tieredRanges', field.name, 'fromValue']);
                              if ((value === undefined || value === null || value === '') && (from === 0 || from === '0')) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.applicableRangeRequired')));
                              }
                              // If TO is a number, it must be >= FROM
                              if (value !== undefined && value !== null && value !== '' && from !== undefined && from !== null && from !== '' && Number(value) < Number(from)) {
                                return Promise.reject(new Error(t('priceModifiers.formulaDescription.endValueMustBeGreaterOrEqual')));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                        className="required-tag-item mb-3 w-2/6"
                        label="End"
                      >
                        <InputNumber
                          min={configureTiersForm.getFieldValue(['tieredRanges', field.name, 'fromValue']) || 1}
                          className="input-number-start"
                          value={configureTiersForm.getFieldValue(['tieredRanges', field.name, 'toValue'])}
                          placeholder={'None'}
                          onChange={(value) => {
                            configureTiersForm.validateFields([["tieredRanges", field.name, "fromValue"]]);

                            if (value === undefined || value === null || value === '') {
                              configureTiersForm.setFieldValue(['tieredRanges', field.name, 'toValue'], undefined);
                            } else {
                              configureTiersForm.setFieldValue(['tieredRanges', field.name, 'toValue'], value);
                            }
                          }}
                          addonBefore={
                            <Form.Item
                              initialValue={'LessThan'}
                              className="mb-0"
                              name={[field.name, 'toOperator']}
                            >
                              <Select
                                className="select-item"
                                suffixIcon={<SelectSuffixIcon />}
                                options={operatorsForTo}
                              />
                            </Form.Item>
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        rules={[
                          {
                            required: true,
                            message: t('priceModifiers.configureTiersForm.amountIsRequired', {
                              Amount: isAmount,
                            }),
                          },
                          {
                            validator: (_, value) => {
                              if (selectedType?.includes('$')) {
                                if (value > 999999999) {
                                  return Promise.reject(
                                    new Error(t('priceModifiers.maximumValueExceeded'))
                                  );
                                } else {
                                  return Promise.resolve();
                                }
                              } else {
                                if (value > 999) {
                                  return Promise.reject(
                                    new Error(t('priceModifiers.maximumPercentExceeded'))
                                  );
                                } else {
                                  return Promise.resolve();
                                }
                              }
                            },
                          },
                        ]}
                        name={[field.name, 'value']}
                        className="required-tag-item mb-3 w-2/6"
                        label={isAmount}
                      >
                        <InputNumber className="input-number-start" addonBefore={selectedType} />
                      </Form.Item>
                    </div>
                    {
                      <Form.Item className="mb-0 mt-4">
                        <Button
                          disabled={fields.length === 1 && index === 0}
                          icon={<FilterBoxDeleteOutlinedIcon />}
                          onClick={() => remove(field.name)}
                          className={
                            'text-red-500 pt-2 cursor-pointer hover:!border-gray-300 h-[40px] !w-[40px]'
                          }
                        />
                      </Form.Item>
                    }
                  </div>
                ))}
              </div>
            </div>
          )}
        </Form.List>
        <Form.Item
          rules={[
            {
              validator: (_, value) => {
                if (selectedType?.includes('$')) {
                  if (value > 999999999) {
                    return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                  } else {
                    return Promise.resolve();
                  }
                } else {
                  if (value > 999) {
                    return Promise.reject(new Error(t('priceModifiers.maximumPercentExceeded')));
                  } else {
                    return Promise.resolve();
                  }
                }
              },
            },
          ]}
          className="w-full mb-0 mt-[25px]"
          label={t('priceModifiers.configureTiersForm.defaultAmountIfNoMatchingTiers', {
            amount: isAmount.toLowerCase(),
          })}
          name={'tieredDefaultValue'}
          initialValue={0}
        >
          <InputNumber className="input-number-start w-full" addonBefore={selectedType} />
        </Form.Item>
      </Form>
    </div>
  );
};
export default ConfigureTieredForm;
