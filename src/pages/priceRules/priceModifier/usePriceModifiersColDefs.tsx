import { useCallback, useMemo } from 'react';
import { IColDef } from '@/types/AgGridTypes.ts';
import { ICellRendererParams } from 'ag-grid-community';
import {
  DeleteIcon,
  EyeIcon,
  HistoryIcon,
  ModifierGridIcon,
  ModifierGroupGridIcon,
} from '@/assets';
import Icon from '@ant-design/icons';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { useLanguage } from '@/hooks/useLanguage';
import { IPriceModifier, IPriceModifierTablePros } from '@/api/priceModifier/priceModifier.types';
import { dateFormatter } from '@/lib/helper/dateHelper';

function escapeRegex(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function useHighlightText() {
  return useCallback(
    (value: string | string[] | null | undefined, search: string): string | JSX.Element => {
      const text = Array.isArray(value) ? value.join(', ') : (value ?? '');

      if (!search || !text) return text;

      const escapedSearch = escapeRegex(search);
      const regex = new RegExp(`(${escapedSearch})`, 'gi');
      const parts = text.split(regex);

      return (
        <span
          dangerouslySetInnerHTML={{
            __html: parts
              .map((part) =>
                regex.test(part) ? `<span style="background-color: yellow;">${part}</span>` : part
              )
              .join(''),
          }}
        />
      );
    },
    []
  );
}

export function usePriceModifierColDefs({
  searchText,
  onEdit,
  onDelete,
  isColumnSortable,
}: IPriceModifierTablePros) {
  const highlightText = useHighlightText();
  const { t } = useLanguage();
  const priceModifierColDefs: IColDef[] = useMemo(
    () => [
      {
        headerName: t('priceModifiers.colDefs.kind'),
        key: 'type',
        field: 'isGroup',
        sortable: isColumnSortable('type'),
        unSortIcon: isColumnSortable('type'),
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.isGroup ? (
            <div className="flex gap-1 items-center">
              <ModifierGroupGridIcon />
              {t('priceModifiers.groupModifiers.priceModifierGroup')}
            </div>
          ) : (
            <div className="flex gap-1 items-center">
              <ModifierGridIcon />
              {t('priceModifiers.groupModifiers.priceModifier')}
            </div>
          );
        },
      },
      {
        headerName: t('priceModifiers.colDefs.name'),
        key: 'name',
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        type: 'string',
        flex: 1,
        field: 'name',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.data.name, searchText) : params.data.name;
        },
      },
      {
        field: 'action',
        headerName: t('addressPage.colDefs.action'),
        pinned: 'right',
        width: 110,
        visible: true,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<IPriceModifier>) => (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                   {params?.data?.createdAt ? dateFormatter(params.data.createdAt) : '-'}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.modified')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                     {params?.data?.updatedAt ? dateFormatter(params.data.updatedAt) : '-'}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.lastUpdatedBy')}:
                  <span className="block w-fit text-sm font-semibold">
                    {params?.data?.updatedByName || t('common.notUpdatedYet')}
                  </span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>{' '}
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view/edit"
              onClick={() => {
                if (onEdit && params.data) {
                  onEdit(params.data);
                }
              }}
            />
            <Icon
              component={DeleteIcon}
              className="cursor-pointer"
              alt="delete"
              onClick={() => {
                if (onDelete && params.data?.id) {
                  onDelete(params.data);
                }
              }}
            />
          </div>
        ),
      },
    ],
    [t, isColumnSortable, searchText, highlightText, onEdit, onDelete]
  );

  return priceModifierColDefs;
}
