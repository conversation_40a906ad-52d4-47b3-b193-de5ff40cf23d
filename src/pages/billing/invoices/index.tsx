import { DeleteIcon, deleteSvg, EmailCredentialIcon, EyeIcon, PlusButtonIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import ColumnManage from '@/components/specific/columnManage';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { GridNames } from '@/types/AppEvents';
import { Button, Divider } from 'antd';
import { Link, useNavigate } from 'react-router-dom';

import { IColDef } from '@/types/AgGridTypes';
import Icon from '@ant-design/icons';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { defaultPagination } from '@/constant/generalConstant';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { advanceFilterObjectMapper, maskQuickFilterData } from '@/lib/SearchFilterTypeManage';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { on } from '@/contexts/PulseContext';
import { useLanguage } from '@/hooks/useLanguage';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { invoicesHook, invoicesService } from '@/api/invoices/useInvoices';
import { InvoiceStatus, IResponseInvoiceDto } from '@/api/invoices/invoices.types';
import { dateFormatter } from '@/lib/helper/dateHelper';
import dayjs from 'dayjs';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { downloadFromBlob } from '@/lib/BlobHelper';

const InvoiceGrid = () => {
  const { t } = useLanguage();
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  const { data: invoiceListing } = invoicesHook.useList(filterParams);
  const [rowData, setRow] = useState<IResponseInvoiceDto[]>([]);
  const gridRef = useRef<AgGridReact<any>>(null);
  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  useEffect(() => {
    if (invoiceListing?.data) {
      setRow(invoiceListing.data as unknown as IResponseInvoiceDto[]);
    }
  }, [invoiceListing]);
  const invoiceStatusCellRenderer = useCallback((params: ICellRendererParams) => {
    const { value: status, data } = params;
    const today = dayjs().startOf('day');

    const isOverdue =
      data?.dueDate &&
      dayjs(data.dueDate).isBefore(today) &&
      status !== InvoiceStatus.Paid &&
      status !== InvoiceStatus.Void;

    // Centralized config
    const statusMap: Record<string, { className: string; label: string }> = {
      [InvoiceStatus.Draft]: { className: 'grey-chip', label: 'Draft' },
      [InvoiceStatus.Sent]: { className: 'primary-chip', label: 'Sent' },
      [InvoiceStatus.Paid]: { className: 'success-chip', label: 'Paid' },
      [InvoiceStatus.PartialPaid]: { className: 'warning-chip', label: 'Partially paid' },
      [InvoiceStatus.Void]: { className: 'error-chip', label: 'Void invoice' },
      Overdue: { className: 'error-chip', label: 'Overdue' },
      PaymentFailed: { className: 'error-chip', label: 'Payment failed' },
    };

    // Overdue check overrides everything
    const display = isOverdue
      ? { className: 'error-chip', label: `${status} - Overdue` }
      : statusMap[status];

    if (!display) return status;

    return (
      <div className="flex justify-center">
        <span className={`${display.className} block w-full`}>{display.label}</span>
      </div>
    );
  }, []);
  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });
  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );
  const notificationManager = useNotificationManager();

  const downloadInvoiceHandler = useCallback(
    async (invoice: IResponseInvoiceDto) => {
      try {
        if (invoice.id) {
          const response = await invoicesService.getById<Blob>(`${invoice.id}/preview`, undefined, {
            responseType: 'blob',
          });
          const url = `${invoice.invoiceNumber}.pdf`;
          downloadFromBlob(response, url);
        } else {
          notificationManager.error({
            message: t('common.error'),
            description: t('invoices.failedToDownloadInvoice'),
          });
        }
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: t('invoices.failedToDownloadInvoice'),
        });
      }
    },
    [notificationManager, t]
  );
  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: 'Download',
        icon: DownloadIcon as React.ElementType,
        key: 'download',
        onClick: (params) => downloadInvoiceHandler(params.rowData),
      },
      {
        label: 'Print',
        icon: EmailCredentialIcon as React.ElementType,
        key: 'print',
        onClick: () => {
          window.print();
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
      },
    ];
  }, [t]);
  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.invoicesGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });
  const navigate = useNavigate();
  const invoiceColDefs: IColDef[] = [
    {
      headerName: t('invoices.invoiceNumber'),
      field: 'invoiceNumber',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'string',
      minWidth: 150,
    },
    {
      headerName: t('ordersPage.status'),
      flex: 1,
      field: 'status',
      visible: true,
      unSortIcon: true,
      type: 'string',
      cellRenderer: invoiceStatusCellRenderer,
      minWidth: 150,
    },
    {
      headerName: t('ordersPage.dateSubmitted'),
      field: 'createdAt',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'date',
      minWidth: 150,
      cellRenderer: (params: ICellRendererParams) => {
        const value = params.value ? dateFormatter(params.value) : '';
        return value;
      },
    },
    {
      headerName: t('addressPage.colDefs.companyName'),
      field: 'companyName',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'string',
      minWidth: 150,
    },
    {
      headerName: t('dashboard.customer.columns.contactName'),
      field: 'customerName',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'string',
      minWidth: 150,
    },
    {
      headerName: t('invoices.dueDate'),
      field: 'dueDate',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'date',
      minWidth: 150,
      cellRenderer: (params: ICellRendererParams) => {
        const value = params.value ? dateFormatter(params.value) : '';
        return value;
      },
    },
    {
      headerName: t('invoices.recipientEmail'),
      field: 'customerEmail',
      visible: true,
      unSortIcon: true,
      flex: 1,
      type: 'string',
      minWidth: 150,
    },
    {
      headerName: t('invoices.totalAmount'),
      field: 'totalAmount',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
      type: 'number',
    },
    {
      headerName: t('invoices.aging'),
      field: 'aging',
      visible: true,
      unSortIcon: true,
      type: 'string',
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('zonePage.colDefs.action'),
      field: 'action',
      visible: true,
      unSortIcon: true,
      flex: 1,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex justify-center gap-2">
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() =>
                navigate(ROUTES.BILLING.BILLING_EDIT_INVOICE.replace(':id', params.data.id))
              }
            />

            <Icon component={DeleteIcon} className="cursor-pointer" alt="delete" />
          </div>
        );
      },
      width: 90,
    },
  ];
  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={'Invoices'} />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                onSearch={triggerSearch}
                colDefs={invoiceColDefs}
                isSetQuickFilter={false}
                searchInputPlaceholder={t('invoices.searchInvoice')}
                onFilterApply={applyFilters}
                setSelectedQuickFilterData={setSelectedQuickFilterData}
                supportedFields={filterableModules.invoices.advanceFilter}
                clearAllFunctionRef={clearAllFunctionRef}
                setFilterParams={setFilterParams}
              />

              <ColumnManage colDefs={invoiceColDefs} gridName={GridNames.invoicesGrid} />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
            </div>
            <div className="pt-0 md:pt-5">
              <Link to={ROUTES.BILLING.BILLING_CREATE_INVOICE}>
                <Button
                  className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                >
                  {t('invoices.createInvoice')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <ActiveFilters
          selectedQuickFilterData={[]}
          clearAllToDefault={() => {}}
          colDefs={invoiceColDefs}
        />
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto pr-6 py-5 flex justify-center items-center">
            <CustomAgGrid
              isContextMenu
              gridName={GridNames.invoicesGrid}
              rowData={rowData}
              columnDefs={invoiceColDefs}
              gridId="gridWrapperForChildren"
              gridRef={gridRef}
              contextMenuItem={customerContextMenuItems}
              onContextMenu={() => {}}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData?.length > 0
                    ? t('common.noMatchesFound')
                    : t('invoices.noInvoicesFound'),
                description:
                  searchText || selectedQuickFilterData?.length > 0
                    ? ''
                    : t('invoices.toGetStarted'),
                link:
                  searchText || selectedQuickFilterData?.length > 0
                    ? ''
                    : t('invoices.addNewInvoice'),
                onLinkAction: () => {
                  navigate(ROUTES.BILLING.BILLING_CREATE_INVOICE);
                },
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default InvoiceGrid;
