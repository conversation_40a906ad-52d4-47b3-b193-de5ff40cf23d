import { customerHook } from '@/api/customer/useCustomer';
import { CollapseUpIcon, DateCalendarIcon, EyeIcon, RefreshInvoiceIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import { IColDef } from '@/types/AgGridTypes';
import Icon from '@ant-design/icons';
import { ICellRendererParams } from 'ag-grid-community';
import { Button, Card, DatePicker, Form, Input, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useState, useRef, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import '../invoice.css';
import { invoicesHook, invoicesService } from '@/api/invoices/useInvoices';
import { InvoiceStatus, IResponseInvoiceDto } from '@/api/invoices/invoices.types';
import dayjs from 'dayjs';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { blobToUrlNavigation, downloadFromBlob } from '@/lib/BlobHelper';
import { useLanguage } from '@/hooks/useLanguage';

const CreateInvoiceComponent = () => {
  const { t } = useLanguage();
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [customerOrderListing, setCustomerOrderListing] = useState<IResponseInvoiceDto[]>([]);
  const [invoiceData, setInvoiceData] = useState<IResponseInvoiceDto>();
  const [selectedOrders, setSelectedOrders] = useState<any[]>([]);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [totalOrders, setTotalOrders] = useState<number>(0);
  const [_isEdit, setIsEdit] = useState<boolean>(false);
  const getSelectedDataRef = useRef<() => any[]>(() => []);
  const [newlySelectedOrders, setNewlySelectedOrders] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState({ preview: false, download: false });
  const [searchText, setSearchText] = useState<{
    searchTextForAssigned: string;
    searchTextForAvailable: string;
    searchTextForSelected: string;
  }>({
    searchTextForAssigned: '',
    searchTextForAvailable: '',
    searchTextForSelected: '',
  });
  const { data: orderListingByCustomerId, refetch: refetchUnbilledOrders } = invoicesHook.useEntity(
    `unbilled-orders/${selectedCustomer}`,
    {
      enabled: Boolean(selectedCustomer),
    }
  );
  const [isStatus, setIsStatus] = useState<{
    isCreate: boolean;
    isView: boolean;
    isEdit: boolean;
  }>({
    isCreate: true,
    isView: false,
    isEdit: false,
  });
  const [selectedStatus, setSelectedStatus] = useState<InvoiceStatus>(InvoiceStatus.Draft);
  const { id } = useParams<{ id: string }>();

  const { data: invoiceDataById } = invoicesHook.useEntity(`${id}`, {
    enabled: Boolean(id),
  });
  const { data: allCustomerList } = customerHook.useEntities('all/minimal');
  const [customerListing, setCustomerListing] = useState<
    {
      value: string;
      label: JSX.Element;
    }[]
  >([]);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const notificationManager = useNotificationManager();
  const createInvoiceMutation = invoicesHook.useCreate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('invoices.invoiceCreated'),
      })
      await refetchUnbilledOrders();
    },
  })
  const updateInvoiceMutation = invoicesHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('invoices.invoiceUpdated'),
      })
    },
  })
  const onFinish = async (buttonStatus?: InvoiceStatus) => {
    const values = await form.validateFields();
    if (!values) {
      return;
    }
    const currentSelectedData = getSelectedDataRef.current();
    const selectedOrdersMerged = currentSelectedData.map((item) => ({
      ...item,
      orderId: item.orderId || item.id
    }));
    const orderIds = selectedOrdersMerged.map(item => item.orderId);
    const data = form.getFieldsValue();
    const formattedDueDate = dayjs(data.dueDate).format('YYYY-MM-DD');
    const formattedInvoiceDate = dayjs(data.invoiceDate).format('YYYY-MM-DD');
    const selectedStatus = data.status;
    const isDefaultStatus = selectedStatus === InvoiceStatus.Draft || selectedStatus === undefined;
    const finalStatus = !isDefaultStatus ? selectedStatus : (buttonStatus || InvoiceStatus.Draft);

    const invoiceData = {
      customerId: selectedCustomer as string,
      invoiceDate: formattedInvoiceDate,
      dueDate: formattedDueDate,
      status: finalStatus,
      notes: data.notes,
      orderIds: orderIds,
      invoiceNumber: data.invoiceNumber,
    };
    if (id) {
      await updateInvoiceMutation.mutateAsync({ id: id, data: invoiceData });
    } else {
      const response = await createInvoiceMutation.mutateAsync(invoiceData) as unknown as IResponseInvoiceDto;
      if (response) {
        navigate(ROUTES.BILLING.BILLING_EDIT_INVOICE.replace(':id', `${response.id}`));
      }
    }
  }
  const generateInvoiceNumber = (): string => {
    const randomNumber = Math.floor(10000 + Math.random() * 90000);
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `INV-${year}-${month}-${day}-${randomNumber}`;
  };

  useEffect(() => {
    const invoiceNumber = generateInvoiceNumber();
    form.setFieldValue('invoiceNumber', invoiceNumber);
  }, [])
  useEffect(() => {
    if (allCustomerList) {
      const formattedResponse = allCustomerList?.data?.map((item) => ({
        value: item.id,
        label: (
          <span>
            {item.companyName} - {item.contactName}
          </span>
        ),
      }));
      setCustomerListing(formattedResponse);
    }
  }, [allCustomerList]);

  useEffect(() => {
    if (id) {
      setIsEdit(true);
      setIsStatus({
        isCreate: false,
        isView: true,
        isEdit: false,
      });

    }
    if (invoiceDataById) {
      const invoiceResponse = invoiceDataById as unknown as IResponseInvoiceDto;
      const totalAmount = invoiceResponse.orders?.reduce((acc, order) => acc + Number(order.totalAmount), 0);
      setTotalAmount(totalAmount);
      setTotalOrders(invoiceResponse.orders?.length || 0);
      setInvoiceData(invoiceResponse);
    }
  }, [id, invoiceDataById]);

  const downloadInvoiceHandler = useCallback(
    async (invoice: IResponseInvoiceDto) => {
      try {
        if (invoice.id) {
          setIsLoading(prev => ({ ...prev, download: true }));
          const response = await invoicesService.getById<Blob>(`${invoice.id}/preview`, undefined, {
            responseType: 'blob',
          });
          const url = `${invoice.invoiceNumber}.pdf`;
          downloadFromBlob(response, url);
        } else {
          notificationManager.error({
            message: t('common.error'),
            description: t('invoices.invoiceDownloadFailed'),
          });
        }
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: t('invoices.invoiceDownloadFailed'),
        });
      }
      finally {
        setIsLoading(prev => ({ ...prev, download: false }));
      }
    },
    [notificationManager, t]
  );

  const viewInvoiceHandler = useCallback(
    async (invoice: IResponseInvoiceDto) => {
      try {
        if (invoice.id) {
          setIsLoading(prev => ({ ...prev, preview: true }));
          const response = await invoicesService.getById<Blob>(`${invoice.id}/preview`, undefined, {
            responseType: 'blob',
          });
          blobToUrlNavigation(response);
        } else {
          notificationManager.error({
            message: t('common.error'),
            description: t('invoices.invoiceIdNotFound'),
          });
        }
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: t('common.somethingWrong'),
        });
      }
      finally {
        setIsLoading(prev => ({ ...prev, preview: false }));
      }
    },
    [notificationManager, t]
  );
  useEffect(() => {
    if (id && invoiceDataById) {
      form.setFieldsValue({
        invoiceNumber: invoiceData?.invoiceNumber,
        invoiceDate: dayjs(invoiceData?.invoiceDate),
        dueDate: dayjs(invoiceData?.dueDate),
        notes: invoiceData?.notes,
        customerId: invoiceData?.customerId,
        status: invoiceData?.status || InvoiceStatus.Draft
      })
      setSelectedStatus(invoiceData?.status || InvoiceStatus.Draft);
      setSelectedCustomer(invoiceData?.customerId as string);
      setSelectedOrders(
        (invoiceData?.orders || []).map((item: any) => ({
          ...item,
          id: (item?.id ?? item?.orderId) ? String(item?.id ?? item?.orderId) : undefined,
        })) as IResponseInvoiceDto[]
      );
    }
  }, [invoiceData])
  useEffect(() => {
    if (orderListingByCustomerId) {
      setCustomerOrderListing(orderListingByCustomerId.data);
    }
  }, [orderListingByCustomerId]);
  useEffect(() => {
    if (selectedCustomer && !id) {
      setSelectedOrders([]);
      setNewlySelectedOrders([]);
    }
  }, [selectedCustomer, id]);
  const onCustomerSelect = (id: string) => {
    setSelectedCustomer(id);
    setSelectedOrders([]);
    setNewlySelectedOrders([]);
  };

  const handleRefreshOrders = async () => {
    if (selectedCustomer) {
      await refetchUnbilledOrders();
    }
  };

  useEffect(() => {
    if (selectedOrders?.length > 0) {
      form.setFieldsValue({
        orderIds: selectedOrders.map(order => order.id)
      });
    }

  }, [selectedOrders, form]);

  useEffect(() => {
    const totalAmount = newlySelectedOrders.reduce((acc, order) => acc + Number(order.totalAmount), 0);
    setTotalAmount(totalAmount);
    setTotalOrders(newlySelectedOrders.length);

  }, [newlySelectedOrders]);

  const getOrderIdForNavigation = (data: any): string => {
    return data.orderId || data.id;
  };

  const ordersColumns: IColDef[] = [
    {
      headerName: t('invoices.trackingId'),
      field: 'trackingNumber',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('priceSetPage.form.serviceLevel'),
      flex: 1,
      field: 'priceSetName',
      visible: true,
      unSortIcon: true,
      minWidth: 150,
    },
    {
      headerName: t('invoices.amount'),
      field: 'totalAmount',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('invoices.completedDate'),
      field: 'actualDeliveryDate',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('ordersPage.status'),
      field: 'status',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('zonePage.colDefs.action'),
      field: 'action',
      visible: true,
      unSortIcon: true,
      flex: 1,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex justify-center gap-2">
            <Icon
              component={EyeIcon}
              className={`${isStatus.isView ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
              alt="view"
              value={'view'}
              onClick={() => {
                if (!isStatus.isView) {
                  const orderId = getOrderIdForNavigation(params.data);
                  navigate(
                    ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', orderId).replace(
                      ':tab',
                      'general'
                    )
                  );
                }
              }}
            />
          </div>
        );
      },
      width: 90,
    },
  ];
  const customGridOrdersColumns: IColDef[] = [
    {
      headerName: t('invoices.trackingId'),
      field: 'trackingNumber',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('priceSetPage.form.serviceLevel'),
      flex: 1,
      field: 'priceSetName',
      visible: true,
      unSortIcon: true,
      minWidth: 150,
    },
    {
      headerName: t('invoices.amount'),
      field: 'totalAmount',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('invoices.completedDate'),
      field: 'actualDeliveryDate',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('ordersPage.status'),
      field: 'status',
      visible: true,
      unSortIcon: true,
      flex: 1,
      minWidth: 150,
    },
    {
      headerName: t('zonePage.colDefs.action'),
      field: 'action',
      visible: true,
      unSortIcon: true,
      flex: 1,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex justify-center gap-2">
            <Icon
              component={EyeIcon}
              className={'cursor-pointer'}
              alt="view"
              value={'view'}
              onClick={() => {
                const orderId = getOrderIdForNavigation(params.data);
                navigate(
                  ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', orderId).replace(
                    ':tab',
                    'general'
                  )
                );
              }
              }
            />
          </div>
        );
      },
      width: 90,
    },
  ];
  const onChangeStatus = (value: string) => {
    form.setFieldsValue({ status: value });
    setSelectedStatus(value as InvoiceStatus);
  }
  return (
    <div className="flex flex-col">
      <PageHeadingComponent
        title={
          isStatus.isEdit ? t('invoices.editInvoice') : isStatus.isView ? t('invoices.viewInvoice') : t('invoices.createInvoice')
        }
        isChildComponent
        onBackClick={() => navigate(ROUTES.BILLING.BILLING_INVOICES_GRID)}
        children={
          <div className="p-3 mt-4 !mb-0">
            {isStatus.isView ? (
              <span>{invoiceData?.status || InvoiceStatus.Draft}</span>
            ) : (
              <Select
                className="w-[200px] h-[40px]"
                value={selectedStatus}
                onChange={(value) => onChangeStatus(value)}
                options={[
                  ...(id && invoiceData?.status !== InvoiceStatus.Draft ? [] : [{
                    value: InvoiceStatus.Draft,
                    label: InvoiceStatus.Draft,
                  }]),
                  {
                    value: InvoiceStatus.Sent,
                    label: InvoiceStatus.Sent,
                  },
                  {
                    value: InvoiceStatus.Paid,
                    label: InvoiceStatus.Paid,
                  },
                  {
                    value: InvoiceStatus.PartialPaid,
                    label: InvoiceStatus.PartialPaid,
                  },
                  {
                    value: InvoiceStatus.Void,
                    label: InvoiceStatus.Void,
                  },
                  {
                    value: InvoiceStatus.Refunded,
                    label: InvoiceStatus.Refunded,
                  },
                ]}
              />
            )}
          </div>
        }
      />

      <div className="invoice-create-content mr-10">
        <Form layout="vertical" form={form} className={`${id ? 'flex flex-col gap-2' : ''}`}>
          {/* Hidden form field to track status */}
          <Form.Item name="status" hidden>
            <Input />
          </Form.Item>
          <div className="first-part-header w-full flex gap-10 flex-col md:flex-row">
            <Form.Item rules={[{ required: true, message: t('invoices.pleaseSelectCustomer') }]} className="w-[35%] general-form-item" label={t('invoices.customer')} name={'customerId'}>
              <Select
                options={customerListing}
                onSelect={(e) => {
                  onCustomerSelect(e);
                }}
                className="h-[40px]"
                placeholder={t('invoices.selectCustomer')}
                suffixIcon={<CollapseUpIcon />}
                disabled={isStatus.isView || isStatus.isEdit}
              />
            </Form.Item>
            <div className="flex gap-10 w-[70%]">
              <Form.Item rules={[{ required: true, message: t('invoices.pleaseSelectInvoiceDate') }]} className="w-[35%] general-form-item" label={t('invoices.invoiceDate')} name={'invoiceDate'}>
                <DatePicker
                  className="w-full h-[40px]"
                  suffixIcon={<DateCalendarIcon />}
                  disabled={isStatus.isView}
                />
              </Form.Item>{' '}
              <Form.Item rules={[{ required: true, message: t('invoices.pleaseSelectInvoiceNumber') }]} className="w-[30%] general-form-item" label={t('invoices.invoiceNumber')} name={'invoiceNumber'}>
                <Input
                  className="w-full h-[40px]"
                  disabled={true}
                  placeholder={t('invoices.invoiceNumberAutoGenerated')}
                />
              </Form.Item>{' '}
              <Form.Item
                dependencies={['invoiceDate']}
                rules={[
                  { required: true, message: t('invoices.pleaseSelectDueDate') },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const invoiceDate = getFieldValue('invoiceDate');
                      if (!value || !invoiceDate) {
                        return Promise.resolve();
                      }
                      if (dayjs(value).isSame(dayjs(invoiceDate)) || dayjs(value).isBefore(dayjs(invoiceDate))) {
                        return Promise.reject(new Error(t('invoices.dueDateAfterInvoiceDate')));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
                className="w-[30%] general-form-item"
                label={t('invoices.dueDate')}
                name={'dueDate'}
              >
                <DatePicker
                  className="w-full h-[40px]"
                  suffixIcon={<DateCalendarIcon />}
                  disabled={isStatus.isView}
                />
              </Form.Item>
            </div>
            <div className="pl-2 pr-[2.5rem] mt-7">
              <Button
                className="h-[40px]"
                title={t('invoices.refreshUnbilledOrders')}
                onClick={handleRefreshOrders}
                disabled={isStatus.isView}
              >
                <RefreshInvoiceIcon />
              </Button>
            </div>
          </div>
          <div className="transfer-grid-invoice">
            {isStatus.isView ? (
              <CustomAgGrid
                gridId={GridIdConstant.GRID_WRAPPER_FOR_EXTRA_SMALL}
                columnDefs={customGridOrdersColumns}
                rowData={invoiceData?.orders}
                className="!h-[50vh] lg:!h-[50vh] 3xl:!h-[55vh]"
              />
            ) : (
              <TransferGrid<any>
                key={selectedCustomer || 'no-customer'}
                colDefs={ordersColumns}
                initialRowData={customerOrderListing}
                setIsEdit={setIsEdit}
                setSearchText={setSearchText}
                searchText={searchText}
                gridProps={{
                  columnDefs: [],
                  className: '!h-[50vh] lg:!h-[50vh] 3xl:!h-[50vh]',
                  gridId: GridIdConstant.GRID_WRAPPER_FOR_INVOICE,
                }}
                isSave={false}
                getSelectedData={getSelectedDataRef}
                saveButtonText="Update Selection"
                hideBackNavigation
                assignedServices={selectedOrders}
                availableGridEmptyStateDescription=""
                availableGridEmptyStateTitle={
                  selectedCustomer == '' ? t('invoices.selectCustomerToViewOrders') : t('ordersPage.noOrdersFound')
                }
                selectedGridEmptyState={
                  selectedCustomer == ''
                    ? t('invoices.addOrdersToInvoice')
                    : t('invoices.selectOrderFromUnbilled')
                }
                mainHeaderTitle=""
                availableGridSearchPlaceholder={t('ordersPage.searchOrders')}
                selectedGridSearchPlaceholder={t('ordersPage.searchOrders')}
                preventEditModeOnSave={false}
                availableGridHeader={t('invoices.unbilledOrders')}
                selectedGridHeader={t('invoices.invoiceOrder')}
                isAdvanceFilter={false}
                setNewlySelectedOrders={setNewlySelectedOrders}
              />
            )}
          </div>
          <div className="second-part-footer flex gap-4 w-full mt-1">
            <div className="left-part-footer flex flex-col w-1/2 gap-5">
              <Form.Item className="w-[94%] !mb-0" label={t('invoices.memo')} name={'notes'}>
                <TextArea
                  rows={4}
                  placeholder={t('paymentOperationsPage.form.placeholders.memoPlaceholder')}
                  disabled={isStatus.isView}
                />
              </Form.Item>
              {isStatus.isView ? (
                <div className="flex gap-4 w-full">
                  {' '}
                  <Button disabled className="h-[40px] w-[20%]">
                    Send
                  </Button>
                  <Button
                    className="h-[40px] w-[20%]"
                    onClick={() => setIsStatus({ isCreate: false, isView: false, isEdit: true })}
                    disabled={
                      !!id && !(invoiceData?.status == InvoiceStatus.Draft || invoiceData?.status === InvoiceStatus.Sent)
                    }                  >
                    Edit invoice
                  </Button>
                  <Button loading={isLoading.download} onClick={() => { downloadInvoiceHandler(invoiceData as IResponseInvoiceDto) }} className="h-[40px] w-[20%]">{t('paymentPage.contextMenu.download')}</Button>
                  <Button onClick={() => navigate(ROUTES.BILLING.BILLING_INVOICES_GRID)} className="h-[40px] w-[20%]">{t('invoices.close')}</Button>
                </div>
              ) : (
                <div className="flex gap-4 w-full">
                  <Button
                    disabled={
                      id && invoiceData?.status !== InvoiceStatus.Draft ||
                      isStatus.isView ||
                      newlySelectedOrders.length === 0
                    }
                    onClick={() => { onFinish(InvoiceStatus.Draft) }}
                    className="h-[40px] w-[22%]"
                  >
                    {t('invoices.saveAsDraft')}
                  </Button>
                  <Button
                    disabled={newlySelectedOrders.length === 0}
                    onClick={() => { onFinish(InvoiceStatus.Sent) }}
                    className="h-[40px] w-[22%]"
                  >
                    {t('invoices.saveAndSend')}
                  </Button>
                  <Button loading={isLoading.preview} disabled={!id} onClick={() => { viewInvoiceHandler(invoiceData as IResponseInvoiceDto) }} className="h-[40px] w-[22%]">{t('invoices.preview')}</Button>
                  <Button onClick={() => navigate(ROUTES.BILLING.BILLING_INVOICES_GRID)} className="h-[40px] w-[22%]">{t('invoices.discardAndClose')}</Button>
                </div>
              )}
            </div>
            <div className="right-part-footer w-[47%] flex gap-7">
              <Card className="w-1/2 bg-[#F5F6FF]">
                <div className="flex  flex-col h-[130px]">
                  <span className="flex justify-between">
                    <span className="font-[600]">{t('invoices.totalOrders')}</span>
                    <span className="font-[600]">{totalOrders}</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">{t('invoices.totalAmountDue')}</span>
                    <span className="font-[600]">${totalAmount}</span>
                  </span>
                </div>
              </Card>
              <Card className="w-1/2 bg-[#F5F6FF]">
                <div className="flex  flex-col h-[130px]">
                  <span className="flex justify-between">
                    <span className="font-[600]">Subtotal</span>
                    <span className="font-[600]">${totalAmount}</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">GST</span>
                    <span className="font-[600]">$0.00</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">QST</span>
                    <span className="font-[600]">$0.00</span>
                  </span>{' '}
                  <span className="flex justify-between">
                    <span className="font-[600]">Total amount</span>
                    <span className="font-[600]">${totalAmount}</span>
                  </span>
                </div>
              </Card>
            </div>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CreateInvoiceComponent;
