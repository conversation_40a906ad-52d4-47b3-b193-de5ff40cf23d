import { customerHook } from '@/api/customer/useCustomer';
import { IResponseInvoiceDto } from '@/api/invoices/invoices.types';
import { invoicesHook } from '@/api/invoices/useInvoices';
import { PaymentMethodEnum, PaymentResponseDto } from '@/api/payments/payments.types';
import { paymentsHook } from '@/api/payments/usePayments';
import { DateCalendarIcon } from '@/assets';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { numberFieldValidator } from '@/lib/FormValidators';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { numberParser } from '@/lib/helper/formHelper';
import { IColDef } from '@/types/AgGridTypes';
import { AgGridReact } from 'ag-grid-react';
import { Button, DatePicker, Form, Input, InputNumber, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

const PaymentOperationalForm = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [invoices, setInvoices] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<IResponseInvoiceDto[]>([]);
  const [totalDue, setTotalDue] = useState<number>(0);
  const gridRef = useRef<AgGridReact>(null);
  const { id: paymentId } = useParams();
  const paymentMethodsOptions = [
    {
      value: PaymentMethodEnum.CreditCard,
      label: t('paymentOperationsPage.paymentMethods.creditCard'),
    },
    {
      value: PaymentMethodEnum.BankTransfer,
      label: t('paymentOperationsPage.paymentMethods.bankTransfer'),
    },
    {
      value: PaymentMethodEnum.Cash,
      label: 'Cash',
    },
    {
      value: PaymentMethodEnum.DebitCard,
      label: 'Debit Card',
    },
    {
      value: PaymentMethodEnum.Other,
      label: 'Other',
    },
  ];

  const { data: customerList } = customerHook.useEntities('all/minimal');
  const { data: paymentDetailsById } = paymentsHook.useEntity(paymentId as string, { enabled: Boolean(paymentId) });
  const [paymentDetails, setPaymentDetails] = useState<PaymentResponseDto>();

  useEffect(() => {
 
    if(!paymentId){
    const totalDue = selectedRows.reduce((acc, invoice) => acc + Number(invoice.pendingAmount), 0);
      setTotalDue(totalDue);
    }
  }, [selectedRows])

  useEffect(() => {
    if (paymentDetailsById) {
      const paymentResponse = paymentDetailsById as unknown as PaymentResponseDto;
      setPaymentDetails(paymentResponse);
      if(paymentId){
        var totalDue = 0;
        for(const invoice of paymentResponse?.invoiceDetails){
          totalDue = totalDue + Number(invoice.pendingAmount);
        }
        setTotalDue(totalDue);
      }
    }
  }, [paymentId, paymentDetailsById])
  useEffect(() => {
    if (paymentDetails) {
      paymentForm.setFieldsValue({
        customerId: paymentDetails.customerId,
        receivedDate: dayjs(paymentDetails.receivedDate),
        referenceNumber: paymentDetails.referenceNumber,
        paymentMethod: paymentDetails.paymentMethod,
        amountPaid: paymentDetails.amountPaid,
        note: paymentDetails.note,
      });
      setInvoices(paymentDetails?.invoiceDetails);
    }
  }, [paymentDetails])
  const customerOptions = useMemo(() => {
    return customerList?.data.map((customer) => ({
      value: customer.id,
      label: `${customer.companyName} - ${customer.contactName} `,
    }));
  }, [customerList?.data]);
  const [paymentForm] = Form.useForm();

  const selectedCustomerId = paymentForm.getFieldValue('customerId');
  const { data: invoiceListingByCustomer } = invoicesHook.useEntity(`customer/${selectedCustomerId}`, { enabled: Boolean(selectedCustomerId) }) as any;
  const customerFieldWatcher = Form.useWatch('customerId', paymentForm);
  useEffect(() => {
    if (invoiceListingByCustomer && !paymentId) {
      setInvoices(invoiceListingByCustomer?.invoices);
    }
    if(!paymentId){
      setTotalDue(0);
    }
  }, [selectedCustomerId, invoiceListingByCustomer]);
  const paymentInvoicesColDefs: IColDef[] = [
    {
      field: 'invoiceNumber',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.invoiceNumber'),
      type: 'string',
      visible: true,
      unSortIcon: true,
      minWidth: 170,
      flex: 1,
    },
    {
      field: 'invoiceDate',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.date'),
      visible: true,
      type: 'date',
      minWidth: 170,
      unSortIcon: true,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return dateFormatter(params.value);
      },
    },
    {
      field: 'subtotal',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.amount'),
      visible: true,
      type: 'number',
      minWidth: 170,
      unSortIcon: true,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return `$${params.value}`;
      },
    },
    {
      field: 'pendingAmount',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.amountDue'),
      visible: true,
      type: 'number',
      unSortIcon: true,
      minWidth: 170,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return `$${params.value}`;
      },
    },
  ];
  const notificationManager = useNotificationManager();
  const createPaymentMutation = paymentsHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Payment created successfully!',
      })
    }
  });
  const updatePaymentMutation = paymentsHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: 'Payment updated successfully!',
      })
    }
  })

  const handleSelectionChanged = () => {
    if (gridRef.current) {
      const selectedNodes = gridRef.current.api.getSelectedNodes();
      const selectedData = selectedNodes.map(node => node.data);
      setSelectedRows(selectedData);
    }
  };

  const handleGridReady = (params: any) => {
    params.api.addEventListener('selectionChanged', () => {
      const selectedNodes = params.api.getSelectedNodes();
      const selectedData = selectedNodes.map((node: any) => node.data);
      setSelectedRows(selectedData);
    });
  };

  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      const api = gridRef.current.api;

      const handleSelectionChange = () => {
        const selectedNodes = api.getSelectedNodes();
        const selectedData = selectedNodes.map((node: any) => node.data);
        setSelectedRows(selectedData);
      };

      api.addEventListener('selectionChanged', handleSelectionChange);

      return () => {
        api.removeEventListener('selectionChanged', handleSelectionChange);
      };
    }
  }, [gridRef.current?.api]);

  const onFinish = async () => {

    const formValidated = paymentForm.validateFields();
    if (!formValidated) {
      return;
    }
    const values = paymentForm.getFieldsValue();
    const receivedDate = dayjs(values.receivedDate).format('YYYY-MM-DD');
    const data = {
      customerId: values.customerId,
      receivedDate: receivedDate,
      invoices: selectedRows.map(invoice => invoice.id),
      referenceNumber: values.referenceNumber,
      paymentMethod: values.paymentMethod,
      amountPaid: values.amountPaid,
      note: values.note
    }
    if (paymentId) {
      await updatePaymentMutation.mutateAsync({ id: paymentId, data });
    } else {
      const result = await createPaymentMutation.mutateAsync(data) as unknown as PaymentResponseDto;
      if (result) {
        navigate(ROUTES.BILLING.BILLING_EDIT_PAYMENT.replace(':id', result.id as string));
      }
    }
  }
  return (
    <div className="">
      <header>
        <PageHeadingComponent
          title={
            paymentId
              ? t('paymentOperationsPage.header.editTitle')
              : t('paymentOperationsPage.header.addTitle')
          }
          isChildComponent={true}
          onBackClick={() => navigate(ROUTES.BILLING.BILLING_PAYMENTS_GRID)}
        />
      </header>
      <main className="mt-6">
        <Form onFinish={onFinish} name="payment-form" layout="vertical" className="custom-form" form={paymentForm}>
          <div className="form-fields-wrapper flex gap-3 flex-col pr-4">
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={t('paymentOperationsPage.form.labels.customer')}
                name="customerId"
                rules={[
                  { required: true, message: t('addressPage.operationalForm.customerError') },
                ]}
              >
                <Select
                  options={customerOptions}
                  allowClear
                  placeholder={t('addressPage.operationalForm.customerPlaceholder')}
                  prefixCls="custom-select"
                  disabled={Boolean(paymentId)}
                  aria-readonly={Boolean(paymentId)}
                  suffixIcon={<SelectDownArrow />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.dateReceived')}
                name="receivedDate"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.dateReceivedRequired'),
                  },
                ]}
              >
                <DatePicker
                  className="custom-date-picker"
                  format={'DD/MM/YYYY'}
                  suffixIcon={<DateCalendarIcon />}
                />
              </Form.Item>
            </div>
            {customerFieldWatcher && (
              <div className={invoices.length === 0 ? 'border border-gray-300 rounded-lg' : ''}>
                <CustomAgGrid
                  ref={gridRef}
                  rowSelection={paymentId ? undefined : {
                    mode: 'multiRow',
                  }}
                  className="!h-[45vh]"
                  gridId={'gridWrapperForPayment'}
                  rowData={invoices}
                  columnDefs={paymentInvoicesColDefs}
                  onGridReady={handleGridReady}
                  onSelectionChanged={handleSelectionChanged}
                  pagination={false}
                  customText={ <div className='text-sm flex gap-4'>{!paymentId &&<span>Account balance: ${invoiceListingByCustomer?.customer?.accountBalance}
                  </span>}
                  <span>Total Amount due:  ${totalDue} </span></div> }
                  emptyState={{
                    title: t('paymentOperationsPage.invoicesGrid.emptyState.title'),
                    description: t('paymentOperationsPage.invoicesGrid.emptyState.description'),
                  }}
                />
              </div>
            )}
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-3">
              <Form.Item
                label={t('paymentOperationsPage.form.labels.referenceNumber')}
                name="referenceNumber"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.referenceNumberRequired'),
                  },
                ]}
              >
                <Input
                  placeholder={t(
                    'paymentOperationsPage.form.placeholders.referenceNumberPlaceholder'
                  )}
                  maxLength={16}
                />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.paymentMethod')}
                name="paymentMethod"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.paymentMethodRequired'),
                  },
                ]}
              >
                <Select
                  options={paymentMethodsOptions}
                  placeholder={t('paymentOperationsPage.form.placeholders.selectPaymentMethod')}
                  prefixCls="custom-select"
                  suffixIcon={<SelectDownArrow />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.paymentAmount')}
                name="amountPaid"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.paymentAmountRequired'),
                  },
                ]}
              >
                <InputNumber
                  inputMode="decimal"
                  formatter={(value) => (value ? `$${value}` : '')}
                  maxLength={12}
                  className="bulk-adjust-input w-full"
                  placeholder={t('paymentOperationsPage.form.placeholders.amountPlaceholder')}
                  onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
                  parser={numberParser}
                  min={1}
                  step={0.01}
                />
              </Form.Item>
            </div>
            <Form.Item label={t('paymentOperationsPage.form.labels.memo')} name="memo">
              <TextArea
                placeholder={t('paymentOperationsPage.form.placeholders.memoPlaceholder')}
              />
            </Form.Item>
          </div>
          {!paymentId && <Button htmlType="submit" type="primary" className="mt-4 px-7 py-4">
            {t('common.add')}
          </Button>}
        </Form>
      </main>
    </div>
  );
};

export default PaymentOperationalForm;
