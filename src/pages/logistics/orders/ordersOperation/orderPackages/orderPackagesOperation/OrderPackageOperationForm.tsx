import { useLanguage } from '@/hooks/useLanguage';
import { IOrderPackageOperationFormProps } from './orderPackagesOperationForm.type';
import { Form, Input, InputNumber, Select } from 'antd';
import { useConfig } from '@/contexts/ConfigContext';
import { Rule, RuleObject } from 'antd/es/form';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { packageTemplateServiceHook } from '@/api/packageTemplates/usePackageTemplate';
import { useMemo } from 'react';
import { formSizeValidator, numberFieldValidator } from '@/lib/FormValidators';
import { isFormChangedHandler } from '@/lib/helper';
import { useNavigationContext } from '@/hooks/useNavigationContext';

const OrderPackageOperationForm = ({ form, onFinish, open }: IOrderPackageOperationFormProps) => {
  const InitialValue = useMemo(() => (open.isEdit ? form.getFieldsValue(true) : {}), [open.isEdit]);
  const dimensionValidator = (_: Rule, value: number) => {
    if (value > 100000) {
      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
    }
    if (value <= 0) {
      return Promise.reject(new Error(t('ordersPage.packagesTab.mustBeMoreThan1')));
    }

    return Promise.resolve();
  };

  const { t } = useLanguage();
  const { config } = useConfig();
  const dimensionFieldsWatcher = Form.useWatch([], form);
  const { data: packageTemplates } = packageTemplateServiceHook.useList();

  const packageOptions = useMemo(() => {
    return packageTemplates?.data?.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }, [packageTemplates]);

  const { setIsBlocked } = useNavigationContext();

  const onPackageSelectHandler = (value: string) => {
    const selectedPackage = packageTemplates?.data?.find((item) => item.id === value);

    if (selectedPackage) {
      form.setFieldsValue({
        length: selectedPackage?.length,
        width: selectedPackage?.width,
        height: selectedPackage?.height,
        totalWeight: selectedPackage?.totalWeight,
        quantity: selectedPackage?.quantity || 1,
      });
    }
  };

  return (
    <div className="w-full md:w-[586px] max-w-[684px]">
      <Form
        name="addCustomPrice"
        className="custom-form"
        layout={'vertical'}
        form={form}
        preserve={false}
        onFinish={onFinish}
        onFieldsChange={(changesFields) => {
          if (changesFields.length <= 1) {
            const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true));
            setIsBlocked(isIsChange);
          } else if (changesFields.length > 1) {
            setIsBlocked(false);
          }
        }}
      >
        <div className="flex flex-col gap-4">
          <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
            <Form.Item
              label={t('ordersPage.packagesTab.FormLabels.packageType')}
              validateFirst
              name="packageTemplateId"
              rules={[
                { required: true, message: t('ordersPage.packagesTab.requiredPackageType') },
                {
                  validator: dimensionValidator,
                },
              ]}
            >
              <Select
                options={packageOptions}
                placeholder={t('ordersPage.packagesTab.placeholderPackageType')}
                prefixCls="custom-select"
                onSelect={onPackageSelectHandler}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              label={t('ordersPage.packagesTab.FormLabels.quantity')}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredQuantity'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="quantity"
            >
              <InputNumber
                inputMode="decimal"
                className="bulk-adjust-input w-full"
                placeholder={t('ordersPage.packagesTab.placeholders.defaultValue')}
                step={1}
                maxLength={5}
                onKeyDown={(event) =>
                  numberFieldValidator(event, { allowDecimals: false, allowNegative: false })
                }
                min={1}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.length')} (${config.units?.dimension})`}
              name="length"
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredLength'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
            >
              <InputNumber
                inputMode="decimal"
                className="bulk-adjust-input w-full"
                placeholder={t('ordersPage.packagesTab.placeholders.defaultValue')}
                step={0.01}
                maxLength={5}
                onKeyDown={(event) =>
                  numberFieldValidator(event, { allowDecimals: true, allowNegative: false })
                }
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.width')} (${config.units?.dimension})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredWidth'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="width"
            >
              <InputNumber
                inputMode="decimal"
                className="bulk-adjust-input w-full"
                placeholder={t('ordersPage.packagesTab.placeholders.defaultValue')}
                step={0.01}
                maxLength={5}
                onKeyDown={(event) =>
                  numberFieldValidator(event, { allowDecimals: true, allowNegative: false })
                }
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.height')} (${config.units?.dimension})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredHeight'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="height"
            >
              <InputNumber
                inputMode="decimal"
                className="bulk-adjust-input w-full"
                placeholder={t('ordersPage.packagesTab.placeholders.defaultValue')}
                step={0.01}
                maxLength={5}
                onKeyDown={(event) =>
                  numberFieldValidator(event, { allowDecimals: true, allowNegative: false })
                }
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.CombinedWeight')} (${config.units?.weight})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredWeight'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="totalWeight"
            >
              <InputNumber
                maxLength={5}
                inputMode="decimal"
                className="bulk-adjust-input w-full"
                placeholder={t('ordersPage.packagesTab.placeholders.defaultValue')}
                step={0.01}
                onKeyDown={(event) =>
                  numberFieldValidator(event, { allowDecimals: true, allowNegative: false })
                }
                min={0}
              />
            </Form.Item>
          </div>
          <Form.Item label={t('ordersPage.packagesTab.FormLabels.CubicDimension')}>
            <Input
              disabled
              value={`${dimensionFieldsWatcher?.length || 0} x ${dimensionFieldsWatcher?.width || 0} x ${dimensionFieldsWatcher?.height || 0} `}
            />
          </Form.Item>

          <Form.Item
            name={'imageUrl'}
            validateTrigger="onChange"
            rules={[
              {
                validator: (_: RuleObject, value) =>
                  formSizeValidator(value, 5, 'Image must be less than 5mb'),
              },
            ]}
          >
            <CustomUpload
              name="imageUrl"
              form={form}
              uploadComponentProps={{ accept: 'image/*' }}
              tooltipTitle={t('ordersPage.packagesTab.tooltips.uploadImage')}
            />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default OrderPackageOperationForm;
