export interface IPriceBreakdown {
  customModifierName: string;
  customModifierAmount: number;
  description: string;
  createdAt: string;
}

export interface ICreatePriceBreakdownDTO {
  customModifierName: string;
  customModifierAmount: string;
}

export interface IResponsePriceBreakdownDto {
  data: IPriceBreakdown[];
}

export interface IModelOpenState {
  open: boolean;
  isEdit: boolean;
  customPriceId: undefined | string;
}
