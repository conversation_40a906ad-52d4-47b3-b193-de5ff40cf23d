import {
  CollapseDownIcon,
  CollapseUpIcon,
  DateCalendarIcon,
  EditPopupIcon,
  infoCircleOutlined,
  LocationOutlinedIcon,
  RightArrowIcon,
} from '@/assets';
import {
  Button,
  Card,
  Checkbox,
  Collapse,
  DatePicker,
  Form,
  Input,
  InputNumber,
  InputRef,
  Radio,
  Select,
  Space,
  Switch,
  TimePicker,
} from 'antd';
import '../orders.css';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import CustomDivider from '@/components/common/divider/CustomDivider';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Autocomplete } from '@react-google-maps/api';
import MaskedInput from 'antd-mask-input';
import TextArea from 'antd/es/input/TextArea';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { AxiosError } from 'axios';
import { TrackedError } from '@/types/AxiosTypes';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { zoneService } from '@/api/zones/useZones';
import { LockIcon, OrdersWarningIcon } from '@/assets';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import usePreventExits from '@/hooks/usePreventExits';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { orderServiceHook } from '@/api/orders/useOrders';
import {
  CreateOrdersDto,
  invoiceStatusOptions,
  IResponseOrderDto,
  IUpdateOrderStatusPayload,
} from '@/api/orders/order.types';
import { convertInUTC, convertInUtcToDayjs, dateFormatter } from '@/lib/helper/dateHelper';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { getOrderStatusText, orderStatusOptions } from '@/constant/generalConstant';
import { useGetDrivers } from '@/api/driver/driver.service';
import { IDrivers } from '@/api/driver/driver.types';
import { OrderStatusEnums } from '@/types/enums/orderStatus';
import OrderStatusSteps from './OrderStatusHistory';
import { orderManagementHooks } from '@/api/orderManagment/useOrderManagement';
import useThrottle from '@/hooks/useThrottle';
import { addressServiceHook } from '@/api/address/useAddress';
import { GetAddressDto } from '@/api/address/address.types';
import { IGetAvailableServices } from '@/api/service/service.types';
import { priceSetHook } from '@/api/priceSet/usePriceSet';
import dayjs from 'dayjs';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { formErrorRegex } from '@/constant/Regex';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';
import { vehicleTypeServiceHook } from '@/api/vehicle/vehicleTypes/useVehicleTypes';
import { Dayjs } from 'dayjs';

interface IFieldsInput {
  isOpenModal: boolean;
  isOpenTitle: string;
  value: string | any;
  type: string;
  fieldName?: string;
  name: string;
  options?: any[];
  onFinish?: (values?: any) => Promise<void>;
}
const OrdersGeneralComponent = () => {
  const [openPanels, setOpenPanels] = useState(['1']);
  const [isOpen, setIsOpen] = useState({
    isOpenModal: false,
    isOpenTitle: '',
    isOpenDescription: '',
  });
  const [isFieldModalOpen, setIsFieldModalOpen] = useState<IFieldsInput>({
    isOpenModal: false,
    isOpenTitle: '',
    value: '',
    type: '',
    fieldName: '',
    name: '',
    onFinish: undefined,
  });
  const { id } = useParams();
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [fieldsForm] = Form.useForm();
  const inputPhoneRef = useRef<InputRef>(null);
  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const autocompleteRef = useRef<Autocomplete>(null);
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [isOpenForAddress, setIsOpenForAddress] = useState<{
    pickUpAddress: boolean;
    deliveryAddress: boolean;
  }>({ pickUpAddress: false, deliveryAddress: false });
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [addressEditType, setAddressEditType] = useState<'pickup' | 'delivery' | null>(null);
  const [pricingChangesForEdit, setPricingChangesForEdit] = useState<{ [key: string]: any }>({});
  const [allAddresses, setAllAddresses] = useState<GetAddressDto[]>([]);
  const [selectedAddresses, setSelectedAddresses] = useState<{
    pickupAddress: GetAddressDto | null;
    deliveryAddress: GetAddressDto | null;
  }>({
    pickupAddress: null,
    deliveryAddress: null,
  });

  const { data: orderDetails, refetch: refetchOrderDetails } =
    orderServiceHook.useEntity<IResponseOrderDto>(id as string, {
      enabled: Boolean(id),
      retry: 0,
    });

  const [shouldCallPricePreview, setShouldCallPricePreview] = useState(false);

  const { data: priceSummary, refetch: refetchPriceSummary } = orderServiceHook.useEntity<any>(
    `${id}/price-preview`,
    {
      enabled: Boolean(id),
    }
  );

  useEffect(() => {
    if (priceSummary && shouldCallPricePreview) {
      if (priceSummary.pricing?.new?.totalPrice !== priceSummary.pricing?.old?.totalPrice) {
        setPricingChangesForEdit({
          ...pricingChangesForEdit,
          oldModifier: priceSummary.pricing.old.modifiers.map((modifier: any) => modifier.name),
          newModifier: priceSummary.pricing.new.modifiers.map((modifier: any) => modifier.name),
          oldPrice: priceSummary.pricing.old.totalPrice,
          newPrice: priceSummary.pricing.new.totalPrice,
        });
      }
      setShouldCallPricePreview(false);
    }
  }, [priceSummary, shouldCallPricePreview]);

  const { data: drivers } = useGetDrivers();
  const { data: vehicleTypes } = vehicleTypeServiceHook.useEntities('no-pagination');
  const { data: allAddressesList, refetch: refetchAllAddresses } = addressServiceHook.useEntities(
    `customer/${orderDetails?.customerId}/all`,
    undefined,
    {
      enabled: Boolean(orderDetails?.customerId),
    }
  );
  useEffect(() => {
    setAllAddresses(allAddressesList as unknown as GetAddressDto[]);
  }, [allAddressesList]);
  const vehiclesOptions = useMemo(() => {
    return vehicleTypes?.data?.map((vehicleType: any) => ({
      value: vehicleType?.id,
      label: vehicleType.name,
    }));
  }, [vehicleTypes]);

  const driversOptions =
    useMemo(() => {
      return drivers?.map((driver: IDrivers) => ({ value: driver.id, label: driver.name }));
    }, [drivers]) || [];
  const { data: priceSets } = priceSetHook.useEntities(
    `${orderDetails?.id}/all-available-services`,
    undefined,
    {
      enabled: Boolean(orderDetails?.id),
    }
  );
  useEffect(() => {
    if (priceSets) {
      setAvailableServices(priceSets);
    }
  }, [priceSets]);

  const notificationManager = useNotificationManager();
  const updateStatusMutation = orderServiceHook.usePatch<IUpdateOrderStatusPayload>();
  const updateOrderMutation = orderServiceHook.useUpdate<Partial<CreateOrdersDto>>();

  // Function to determine initial modifier state based on configuration
  const getInitialModifierState = useCallback((modifier: any, customPricingSummary?: any, currentPriceSetId?: string, serviceId?: string) => {
    if (customPricingSummary?.modifiers && currentPriceSetId && serviceId === currentPriceSetId) {
      const orderModifier = customPricingSummary.modifiers.find((om: any) => om.id === modifier.id);
      if (orderModifier && orderModifier.hasCustomerSelected !== undefined) {
        return orderModifier.hasCustomerSelected;
      }
    }
    
    switch (modifier.configuration) {
      case 'required':
        return true;
      case 'selected':
        return true;
      case 'unselected':
        return false;
      default:
        return false;
    }
  }, []);

  // Function to check if modifier should be disabled
  const isModifierDisabled = useCallback((modifier: any) => {
    return modifier.configuration === 'required';
  }, []);

  const calculatePriceWithModifiers = useCallback(
    (selectedService: any, modifierSelections: { [modifierId: string]: boolean }) => {
      if (!selectedService?.pricing?.modifiers) {
        return {
          selectedModifiers: [],
          disSelectedModifiers: [],
          modifiersTotal: 0,
          basePrice: selectedService?.pricing?.basePrice || 0,
          totalAmount: selectedService?.pricing?.basePrice || 0,
        };
      }

      const allModifiers = selectedService.pricing.modifiers;
      const basePrice = selectedService.pricing.basePrice || 0;

      const selectedModifiers: any[] = [];
      const disSelectedModifiers: any[] = [];

      allModifiers.forEach((modifier: any) => {
        if (modifier.configuration === 'required') {
          selectedModifiers.push(modifier);
        } else if (modifier.configuration === 'selected') {
          if (modifierSelections[modifier.id] === false) {
            disSelectedModifiers.push(modifier);
          } else {
            selectedModifiers.push(modifier);
          }
        } else if (modifier.configuration === 'unselected') {
          if (modifierSelections[modifier.id] === true) {
            selectedModifiers.push(modifier);
          } else {
            disSelectedModifiers.push(modifier);
          }
        }
      });

      // Calculate modifiers total
      const modifiersTotal = selectedModifiers.reduce(
        (sum: number, modifier: any) => sum + (modifier.amount || 0),
        0
      );

      // Calculate total amount
      const totalAmount = basePrice + modifiersTotal;

      return {
        selectedModifiers,
        disSelectedModifiers,
        modifiersTotal,
        basePrice,
        totalAmount,
      };
    },
    []
  );

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);
  const [toggles, setToggles] = useState({
    isCod: false,
    isInsurance: false,
    signatureRequiredForPickup: false,
    signatureRequiredForDelivery: false,
  });

  interface IUpdateOrderParams {
    payload: Partial<CreateOrdersDto>;
    showNotification: boolean;
  }

  const isOrderLocked = useMemo(() => orderDetails?.isLocked, [orderDetails?.isLocked]);
  const createCustomerAddressMutation = addressServiceHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successCreate'),
      });
      refetchAllAddresses();
      setShouldCallPricePreview(true);
      refetchPriceSummary();
    },
  });

  const updateCustomerAddressMutation = addressServiceHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('customerAddressPage.notifications.successUpdate'),
      });
      refetchAllAddresses();
      setShouldCallPricePreview(true);
      refetchPriceSummary();
    },
  });

  const saveNewAddress = async () => {
    const values = await form.validateFields();

    await form.validateFields();

    // Create a new address
    const newAddress = await createCustomerAddressMutation.mutateAsync({
      ...values,
      name: values.name,
      countryCode: values.countryCode,
      phoneNumber: values.phoneNumber,
      phoneExtension: values.phoneExtension || null,
      phoneNumberPhoneExtension: values.phoneNumberPhoneExtension || null,
      faxNumberPhoneExtension: values.faxNumberPhoneExtension || null,
      customerId: orderDetails?.customerId,
    });

    // Update the order with the new address
    const formattedAddress = [
      values.addressLine1,
      values.addressLine2,
      values.city,
      values.province,
      values.postalCode,
      values.country,
    ]
      .filter(Boolean)
      .join(', ');

    const zoneInfo = {
      zoneId: values.zoneId || '',
      zoneName: values.zone || '',
    };

    const payload =
      addressEditType === 'pickup'
        ? {
            collectionAddress: formattedAddress,
            collectionCompanyName: values.companyName,
            collectionContactName: values.name,
            collectionEmail: values.email,
            collectionPhone: values.phoneNumber,
            collectionPhoneExtension: values.phoneExtension || null,
            collectionPhoneNumberPhoneExtension: values.phoneNumberPhoneExtension || null,
            collectionFaxNumberPhoneExtension: values.faxNumberPhoneExtension || null,
            collectionInstructions: values.notes,
            collectionAddressId: newAddress.id,
            collectionZoneId: zoneInfo.zoneId,
            collectionZoneName: zoneInfo.zoneName,
          }
        : {
            deliveryAddress: formattedAddress,
            deliveryCompanyName: values.companyName,
            deliveryContactName: values.name,
            deliveryEmail: values.email,
            deliveryPhone: values.phoneNumber,
            deliveryPhoneExtension: values.phoneExtension || null,
            deliveryPhoneNumberPhoneExtension: values.phoneNumberPhoneExtension || null,
            deliveryFaxNumberPhoneExtension: values.faxNumberPhoneExtension || null,
            deliveryInstructions: values.notes,
            deliveryAddressId: newAddress.id,
            deliveryZoneId: zoneInfo.zoneId,
            deliveryZoneName: zoneInfo.zoneName,
          };

    await updateOrderHandler({ payload, showNotification: true });

    // Close the modal after successful update
    setIsOpen({ ...isOpen, isOpenModal: false });
    onAddressModalClose();
    setShouldCallPricePreview(true);
    refetchPriceSummary();
  };

  const editExistingAddress = async () => {
    const values = await form.validateFields();
    await form.validateFields();

    const addressId =
      values.id ||
      (addressEditType === 'pickup'
        ? selectedAddresses?.pickupAddress?.id
        : selectedAddresses?.deliveryAddress?.id);

    const formattedAddress = [
      values.addressLine1,
      values.addressLine2,
      values.city,
      values.province,
      values.postalCode,
      values.country,
    ]
      .filter(Boolean)
      .join(', ');

    const zoneInfo = {
      zoneId: values.zoneId || '',
      zoneName: values.zone || '',
    };

    const payload =
      addressEditType === 'pickup'
        ? {
            collectionAddress: formattedAddress,
            collectionCompanyName: values.companyName,
            collectionContactName: values.name,
            collectionEmail: values.email,
            collectionPhone: values.phoneNumber,
            collectionPhoneExtension: values.phoneExtension || null,
            collectionPhoneNumberPhoneExtension: values.phoneNumberPhoneExtension || null,
            collectionFaxNumberPhoneExtension: values.faxNumberPhoneExtension || null,
            collectionInstructions: values.notes,
            collectionAddressId: addressId,
            collectionZoneId: zoneInfo.zoneId,
            collectionZoneName: zoneInfo.zoneName,
          }
        : {
            deliveryAddress: formattedAddress,
            deliveryCompanyName: values.companyName,
            deliveryContactName: values.name,
            deliveryEmail: values.email,
            deliveryPhone: values.phoneNumber,
            deliveryPhoneExtension: values.phoneExtension || null,
            deliveryPhoneNumberPhoneExtension: values.phoneNumberPhoneExtension || null,
            deliveryFaxNumberPhoneExtension: values.faxNumberPhoneExtension || null,
            deliveryInstructions: values.notes,
            deliveryAddressId: addressId,
            deliveryZoneId: zoneInfo.zoneId,
            deliveryZoneName: zoneInfo.zoneName,
          };

    await updateOrderHandler({ payload, showNotification: true });

    // Close the modal after successful update
    setIsOpen({ ...isOpen, isOpenModal: false });
    onAddressModalClose();
    setShouldCallPricePreview(true);
    refetchPriceSummary();
  };

  const updateOrderHandler = useThrottle(
    async ({ payload, showNotification = true }: IUpdateOrderParams) => {
      if (isOrderLocked) return;
      if (payload.codAmount) {
        payload.codAmount = fieldsForm.getFieldValue('codAmount')
          ? parseFloat(fieldsForm.getFieldValue('codAmount'))
          : undefined;
      }

      const sanitizedPayload = {
        ...payload,
        codCollectionType: fieldsForm.getFieldValue('codCollectionType') || undefined,
      };
      const fieldKey = Object.keys(payload)[0] as keyof CreateOrdersDto;

      const newValue = sanitizedPayload[fieldKey];
      if (fieldKey === 'scheduledCollectionTime' || fieldKey === 'scheduledDeliveryTime') {
        sanitizedPayload[fieldKey] = convertInUTC(newValue as Dayjs);
      }

      try {
        if (isFieldModalOpen.onFinish) {
          await isFieldModalOpen.onFinish(sanitizedPayload);
        } else {
          await updateOrderMutation.mutateAsync({
            id: orderDetails?.id as string,
            data: sanitizedPayload,
          });
        }

        if (!isFieldModalOpen.onFinish && showNotification) {
          notificationManager.success({
            message: t('common.success'),
            description: 'Order updated successfully',
          });
        }

        setIsFieldModalOpen({
          isOpenModal: false,
          isOpenTitle: '',
          value: '',
          type: '',
          fieldName: '',
          name: '',
          onFinish: undefined,
        });

        await refetchOrderDetails();

        if (payload.priceSetId) {
          setShouldCallPricePreview(true);
          refetchPriceSummary();
        }
        setShouldCallPricePreview(true);
        refetchPriceSummary();
      } catch {
        //
      }
    },
    3000
  );

  const canAssignDriver = useMemo(() => {
    return (
      orderDetails?.status !== OrderStatusEnums.DRAFT &&
      orderDetails?.status !== OrderStatusEnums.CANCELLED &&
      orderDetails?.status !== OrderStatusEnums.COMPLETED &&
      orderDetails?.status !== OrderStatusEnums.IN_TRANSIT &&
      !isOrderLocked
    );
  }, [isOrderLocked, orderDetails?.status]);

  useEffect(() => {
    if (orderDetails) {
      fieldsForm.setFieldsValue({
        ...orderDetails,
        codCollectionType: orderDetails?.codCollectionType || 'Delivery',
        submittedAt: convertInUtcToDayjs(orderDetails?.submittedAt),
        scheduledCollectionTime: convertInUtcToDayjs(orderDetails?.scheduledCollectionTime),
        scheduledDeliveryTime: convertInUtcToDayjs(orderDetails?.scheduledDeliveryTime),
      });
      setToggles({
        isCod: orderDetails?.isCod || false,
        isInsurance: orderDetails?.isInsurance || false,
        signatureRequiredForPickup: orderDetails?.collectionSignatureRequired || false,
        signatureRequiredForDelivery: orderDetails?.deliverySignatureRequired || false,
      });
    }
  }, [fieldsForm, orderDetails]);

  const assignOrderMutation = orderManagementHooks.useAssignOrderToDriver();

  const assignDriverHandler = async (value: { assignedDriverId: string }) => {
    await assignOrderMutation.mutateAsync({
      orderId: id as string,
      driverId: value.assignedDriverId,
    });
  };

  const changeOrderStatusHandler = async ({
    internalStatus,
  }: {
    internalStatus: OrderStatusEnums;
  }) => {
    await updateStatusMutation.mutateAsync({
      id: `${id}/status`,
      data: {
        status: internalStatus,
        reason: 'N/A',
        comments: 'changed from admin panel',
      },
    });
  };
  const printTotalPackages = () => {
    return orderDetails?.items?.length !== 1
      ? `${orderDetails?.items?.length} Packages`
      : `${orderDetails?.items?.length} Package`;
  };

  const { setPreventExit } = usePreventExits();
  const [availableServices, setAvailableServices] = useState<any>();
  const [selectedDeliveryService, setSelectedDeliveryService] =
    useState<IGetAvailableServices | null>(null);
  const [selectedModifiers, setSelectedModifiers] = useState<{
    [serviceId: string]: { [modifierId: string]: boolean };
  }>({});
  const { isBlocked, setIsBlocked } = useNavigationContext();
  const navigate = useNavigate();

  useEffect(() => {
    if (orderDetails?.priceSetId && availableServices?.data) {
      const existingPriceSet = availableServices.data.find(
        (priceSet: any) => priceSet.id === orderDetails.priceSetId
      );
      if (existingPriceSet) {
        setSelectedDeliveryService(existingPriceSet);
        form.setFieldValue('priceSetId', existingPriceSet.id);
      }
    }
  }, [orderDetails?.priceSetId, availableServices?.data, form]);

  useEffect(() => {
    if (selectedDeliveryService && selectedDeliveryService.pricing?.modifiers) {
      setSelectedModifiers((prev) => {
        if (prev[selectedDeliveryService.id]) {
          return prev;
        }

        const initialModifierStates: { [modifierId: string]: boolean } = {};
        const selectedModifierIds: string[] = [];

        selectedDeliveryService.pricing.modifiers.forEach((modifier: any) => {
          const isSelected = getInitialModifierState(
            modifier, 
            orderDetails?.customPricingSummary, 
            orderDetails?.priceSetId, 
            selectedDeliveryService.id
          );
          initialModifierStates[modifier.id] = isSelected;
          if (isSelected) {
            selectedModifierIds.push(modifier.id);
          }
        });

        return {
          ...prev,
          [selectedDeliveryService.id]: initialModifierStates,
        };
      });
    }
  }, [selectedDeliveryService?.id, getInitialModifierState, orderDetails?.customPricingSummary, orderDetails?.priceSetId]); // Include order details dependencies

  // Initialize form with order details when editing
  useEffect(() => {
    if (orderDetails && availableServices?.data) {
      // Set the price set field value for editing
      if (orderDetails.priceSetId) {
        form.setFieldValue('priceSetId', orderDetails.priceSetId);
      }
    }
  }, [orderDetails, availableServices?.data, form]);

  // Function to handle modifier selection change
  const handleModifierChange = useCallback(
    (serviceId: string, modifierId: string, checked: boolean) => {
      setSelectedModifiers((prev) => {
        const newSelectedModifiers = {
          ...prev,
          [serviceId]: {
            ...prev[serviceId],
            [modifierId]: checked,
          },
        };

        // Recalculate price when modifier changes
        const selectedService = availableServices?.data?.find(
          (service: any) => service.id === serviceId
        );
        if (selectedService) {
          const calculationResult = calculatePriceWithModifiers(
            selectedService,
            newSelectedModifiers[serviceId]
          );

          // Update the selected delivery service with new pricing
          setSelectedDeliveryService((prev) =>
            prev?.id === serviceId
              ? {
                  ...prev,
                  pricing: {
                    ...prev.pricing,
                    totalPrice: calculationResult.totalAmount,
                    totalModifierPrice: calculationResult.modifiersTotal,
                  },
                }
              : prev
          );
        }

        return newSelectedModifiers;
      });
    },
    [availableServices?.data, calculatePriceWithModifiers]
  );
  const handleSelect = async (address: GetAddressDto, type: string) => {
    setSelectedAddresses((prev) => ({ ...prev, [type]: address }));
    if (type === 'pickupAddress') {
      setSelectedAddresses((prev) => ({
        ...prev,
        pickupAddress: {
          id: address.id,
          createdAt: '',
          updatedAt: '',
          createdBy: '',
          updatedBy: '',
          name: address.name,
          companyName: address.companyName,
          email: address.email,
          countryCode: address.countryCode,
          phoneNumber: address.phoneNumber,
          phoneExtension: address.phoneExtension,
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2,
          city: address.city,
          province: address.province,
          postalCode: address.postalCode,
          latitude: address.latitude,
          longitude: address.longitude,
          zone: address.zone,
          country: address.country,
          notes: address.notes,
          isFavoriteForPickup: address.isFavoriteForPickup,
          isFavoriteForDelivery: address.isFavoriteForDelivery,
          isDefaultForPickup: address.isDefaultForPickup,
          isDefaultForDelivery: address.isDefaultForDelivery,
          customerId: address.customerId,
        },
      }));
    } else {
      setSelectedAddresses((prev) => ({
        ...prev,
        deliveryAddress: {
          id: address.id,
          createdAt: '',
          updatedAt: '',
          createdBy: '',
          updatedBy: '',
          name: address.name,
          companyName: address.companyName,
          email: address.email,
          countryCode: address.countryCode,
          phoneNumber: address.phoneNumber,
          phoneExtension: address.phoneExtension,
          addressLine1: address.addressLine1,
          addressLine2: address.addressLine2,
          city: address.city,
          province: address.province,
          postalCode: address.postalCode,
          latitude: address.latitude,
          longitude: address.longitude,
          zone: address.zone,
          country: address.country,
          notes: address.notes,
          isFavoriteForPickup: address.isFavoriteForPickup,
          isFavoriteForDelivery: address.isFavoriteForDelivery,
          isDefaultForPickup: address.isDefaultForPickup,
          isDefaultForDelivery: address.isDefaultForDelivery,
          customerId: address.customerId,
        },
      }));
    }
    const addressString = `${address.name}${address.companyName ? ', ' + address.companyName : ''}`;
    form.setFieldValue(`addressSelect`, addressString);
    form.setFieldsValue({
      name: address.name || '',
      companyName: address.companyName || '',
      addressLine1: address.addressLine1 || '',
      addressLine2: address.addressLine2 || '',
      city: address.city || '',
      province: address.province || '',
      postalCode: address.postalCode || '',
      country: address.country || '',
      email: address.email || '',
      phoneNumber: address.phoneNumber || '',
      phoneExtension: address.phoneExtension || '',
      notes: address.notes || '',
      zone: address.zone || '',
      countryCode: address.countryCode || 'CAN',
      id: address.id,
    });
    maskingInputPhone(address.countryCode || 'CAN', false);

    try {
      form.resetFields(['zone', 'zoneId']);
      if (address.postalCode) {
        const trimmedPostalCode = address.postalCode.split(' ')[0];
        const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
        form.setFieldValue('zone', response.name);
        form.setFieldValue('zoneId', response.id);
        await form.validateFields(['zone']);
      } else {
        form.setFields([
          {
            name: 'zone',
            errors: [t('addressPage.operationalForm.noPostalCodeFound')],
          },
        ]);
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        const errorStack = error?.response?.data as TrackedError;
        if (errorStack?.code === 406007) {
          form.resetFields(['zone', 'zoneId']);
          form.setFields([
            {
              name: 'zone',
              errors: [
                t('addressPage.operationalForm.zoneError', {
                  code: errorStack?.details?.postalCode.split(' ')[0],
                }),
              ],
            },
          ]);
        }
      }
    }

    setIsOpenForAddress((prev) => ({ ...prev, pickUpAddress: false }));

    // Reset form change state since we're just selecting an existing address
    setIsFormChanged(false);
  };

  const orderInfoData = {
    Column: [
      {
        key: 'trackingNo',
        label: t('ordersPage.orderInfoDataFields.trackingNo'),
        value: (
          <span className="flex gap-3 w-max">
            {isOrderLocked && <img src={LockIcon} />}
            {orderDetails?.trackingNumber}
          </span>
        ),
      },
      {
        key: 'dateSubmitted',
        label: t('ordersPage.orderInfoDataFields.dateSubmitted'),
        value: orderDetails?.submittedAt ? dateFormatter(orderDetails?.submittedAt) : 'N/A',
        editable: true,
        children: !isOrderLocked && (
          <Button
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.dateSubmitted'),
                value: orderDetails?.submittedAt
                  ? convertInUtcToDayjs(orderDetails?.submittedAt)
                  : undefined,
                type: 'date',
                fieldName: 'Date',
                name: 'submittedAt',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'packages',
        label: t('ordersPage.orderInfoDataFields.numberOfPackages'),
        value: orderDetails?.items?.length ? printTotalPackages() : 'N/A',
        children: (
          <img
            className="px-1 py-[8px] cursor-pointer"
            src={RightArrowIcon}
            onClick={() =>
              navigate(
                ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', id as string).replace(
                  ':tab',
                  'packages'
                )
              )
            }
          />
        ),
      },
      {
        key: 'pickupDate&Time',
        label: t('ordersPage.orderInfoDataFields.pickupDateTime'),
        value: orderDetails?.scheduledCollectionTime
          ? dateFormatter(orderDetails?.scheduledCollectionTime)
          : 'N/A',
        editable: true,
        children: !isOrderLocked && (
          <Button
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: 'Pickup date & time',
                value: orderDetails?.scheduledCollectionTime
                  ? convertInUtcToDayjs(orderDetails?.scheduledCollectionTime)
                  : undefined,
                type: 'date',
                name: 'scheduledCollectionTime',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'signature',
        label: t('ordersPage.orderInfoDataFields.pickupSignatureRequired'),
        value: toggles?.signatureRequiredForPickup ? t('common.yes') : t('common.no'),
        children: (
          <Switch
            className="order-collapse-switch"
            disabled={updateOrderMutation.isPending || isOrderLocked}
            value={toggles?.signatureRequiredForPickup || false}
            onChange={(value) => {
              updateOrderHandler({
                payload: { collectionSignatureRequired: value },
                showNotification: false,
              });
            }}
          />
        ),
      },
      {
        key: 'dateDeliveryBy',
        label: t('ordersPage.orderInfoDataFields.deliveryByDate'),
        value: orderDetails?.scheduledDeliveryTime
          ? dateFormatter(orderDetails?.scheduledDeliveryTime)
          : 'N/A',
        editable: true,
        children: !isOrderLocked && (
          <Button
          disabled={isOrderLocked}
            onClick={() =>
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.deliveryByDate'),
                value: orderDetails?.scheduledDeliveryTime
                  ? convertInUtcToDayjs(orderDetails?.scheduledDeliveryTime)
                  : undefined,
                type: 'date',
                name: 'scheduledDeliveryTime',
              })
            }
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'cod',
        label: t('ordersPage.orderInfoDataFields.cod'),
        value: toggles.isCod ? t('common.yes') : t('common.no'),
        children: (
          <Switch
            className="order-collapse-switch"
            value={toggles?.isCod}
            disabled={updateOrderMutation.isPending || isOrderLocked}
            onChange={(value) =>
              updateOrderHandler({
                payload: {
                  isCod: value,
                },
                showNotification: false,
              })
            }
          />
        ),
      },
      {
        key: 'signatureRequired',
        label: t('ordersPage.orderInfoDataFields.deliverySignatureRequired'),
        value: toggles?.signatureRequiredForDelivery ? t('common.yes') : t('common.no'),
        type: 'toggle',
        children: (
          <Switch
            className="order-collapse-switch"
            value={toggles?.signatureRequiredForDelivery || isOrderLocked}
            disabled={updateOrderMutation.isPending || isOrderLocked}
            onChange={(value) =>
              updateOrderHandler({
                payload: { deliverySignatureRequired: value },
                showNotification: false,
              })
            }
          />
        ),
      },
      {
        key: 'vehicleType',
        label: t('ordersPage.orderInfoDataFields.vehicle'),
        value: orderDetails?.vehicleTypeName || 'N/A',
        children: !isOrderLocked && (
          <Button
          disabled={isOrderLocked}
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.vehicle'),
                value: orderDetails?.vehicleTypeId || undefined,
                type: 'dropdown',
                name: 'vehicleTypeId',
                options: vehiclesOptions,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'codAmount',
        label: t('ordersPage.orderInfoDataFields.codAmount'),
        value: orderDetails?.codAmount ? `$${orderDetails?.codAmount || 0}` : 'N/A',
        editable: true,
        children: !isOrderLocked && (
          <Button
          disabled={isOrderLocked}
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: 'COD',
                value: orderDetails?.codAmount || undefined,
                type: 'isCod',
                name: 'codAmount',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'description',
        label: t('ordersPage.orderInfoDataFields.description'),
        value: orderDetails?.description || 'N/A',
        children: !isOrderLocked && (
          <Button
          disabled={isOrderLocked}
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.description'),
                value: orderDetails?.description,
                type: 'textarea',
                name: 'description',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'assignedDriverId',
        label: t('ordersPage.orderInfoDataFields.driverName'),
        value: orderDetails?.assignedDriverName || 'N/A',
        editable: true,
        children: canAssignDriver && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.driverName'),
                value: orderDetails?.assignedDriverId || undefined,
                type: 'dropdown',
                name: 'assignedDriverId',
                options: driversOptions,
                onFinish: assignDriverHandler,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'internalStatus',
        label: t('ordersPage.orderInfoDataFields.status'),
        value: orderDetails?.internalStatus
          ? getOrderStatusText(orderDetails?.internalStatus)
          : 'N/A',
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.status'),
                value: orderDetails?.internalStatus || undefined,
                type: 'dropdown',
                name: 'internalStatus',
                options: orderStatusOptions,
                onFinish: changeOrderStatusHandler,
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },

      {
        key: 'notes',
        label: t('ordersPage.orderInfoDataFields.notes'),
        value: orderDetails?.internalNotes || 'N/A',
        editable: true,
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderInfoDataFields.notes'),
                value: orderDetails?.internalNotes || 'N/A',
                type: 'textarea',
                name: 'internalNotes',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
    ],
  };
  const orderPriceData = {
    Column: [
      {
        key: 'serviceLevel',
        label: t('ordersPage.orderPriceDataFields.serviceLevel'),
        value: orderDetails?.serviceLevel || 'N/A',
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.priceSet'),
                value: orderDetails?.priceSet || 'N/A',
                type: 'priceSet',
                fieldName: '',
                name: 'priceSetId',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'priceSet',
        label: t('ordersPage.orderPriceDataFields.priceSet'),
        value: orderDetails?.priceSet || 'N/A',
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.priceSet'),
                value: orderDetails?.priceSetId || '',
                type: 'priceSet',
                fieldName: '',
                name: 'priceSetId',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'basePrice',
        label: t('ordersPage.orderPriceDataFields.basePrice'),
        value: `$${orderDetails?.basePrice || 0}`,
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.basePrice'),
                value: orderDetails?.basePrice,
                type: 'number',
                name: 'basePrice',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'optionsPrice',
        label: t('ordersPage.orderPriceDataFields.optionsPrice'),
        value: `$${orderDetails?.optionsPrice || 0}`,
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.optionsPrice'),
                value: orderDetails?.optionsPrice,
                type: 'number',
                name: 'optionsPrice',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'total',
        label: t('ordersPage.orderPriceDataFields.total'),
        value: `$${orderDetails?.totalPrice || 0}`,
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.total'),
                value: orderDetails?.totalPrice,
                type: 'number',
                name: 'totalPrice',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'miscAdjustment',
        label: t('ordersPage.orderPriceDataFields.miscAdjustment'),
        value: `$${orderDetails?.miscAdjustment || 0}`,
      },
         {
        key: 'billingStatus',
        label: t('ordersPage.orderPriceDataFields.billingStatus'),
        value: orderDetails?.billingStatus || 'N/A',
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.billingStatus'),
                  value: orderDetails?.billingStatus || 'Not_Billed',
                type: 'dropdown',
                name: 'billingStatus',
                 options: [
            { label: 'Billed', value: 'Billed' },
            { label: 'Not Billed', value: 'Not_Billed' },
          ],
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
        // children: (
        //   <Button
        //     onClick={() => {
        //       setIsFieldModalOpen({
        //         isOpenModal: true,
        //         isOpenTitle: t('ordersPage.orderPriceDataFields.billingStatus'),
        //         value: orderDetails?.billingStatus || 'N/A',
        //         type: 'billingStatus',
        //         name: 'billingStatus',
        //       });
        //     }}
        //     className="border-none"
        //     icon={<EditPopupIcon />}
        //   />
        // ),
      },
      {
        key: 'totalDeclaredValue',
        label: t('ordersPage.orderPriceDataFields.totalDeclaredValue'),
        value: orderDetails?.declaredValue ? `$${orderDetails?.declaredValue}` : 'N/A',
        children: !isOrderLocked && (
          <Button
            onClick={() => {
              setIsFieldModalOpen({
                isOpenModal: true,
                isOpenTitle: t('ordersPage.orderPriceDataFields.totalDeclaredValue'),
                value: Number(orderDetails?.declaredValue) || 0,
                type: 'number',
                name: 'declaredValue',
              });
            }}
            className="border-none"
            icon={<EditPopupIcon />}
          />
        ),
      },
      {
        key: 'invoiceNumber',
        label: t('ordersPage.orderPriceDataFields.invoiceNumber'),
        value: orderDetails?.invoiceNumber || 'N/A',
      },

    {
  key: 'paymentStatus',
  label: t('ordersPage.orderPriceDataFields.paymentStatus'),
  value: orderDetails?.invoiceStatus || 'N/A',
  children: !isOrderLocked && (
    <Button
      onClick={() => {
        setIsFieldModalOpen({
          isOpenModal: true,
          isOpenTitle: t('ordersPage.orderPriceDataFields.paymentStatus'),
          value: orderDetails?.invoiceStatus || 'N/A',
          type: 'dropdown',
          name: 'invoiceStatus',
          options: invoiceStatusOptions,
        });
      }}
      className="border-none"
      icon={<EditPopupIcon />}
      disabled={!isOrderLocked}
      style={!isOrderLocked ? { opacity: 0.5, pointerEvents: 'none' } : {}}
    />
  ),
},
    ],
  };
  const expectedDeliveryFormat = (deliveryDate: string) => {
    const diff = dayjs(deliveryDate).startOf('day').diff(dayjs().startOf('day'), 'day');

    switch (diff) {
      case 0:
        return `Expected delivery today by ${dayjs(deliveryDate).format('hh:mm A')}.`;
      case 1:
        return `Expected delivery by tomorrow ${dayjs(deliveryDate).format('hh:mm A')}.`;
      default:
        return `Expected delivery by ${dayjs(deliveryDate).format('DD/MM/YYYY hh:mm A')}.`;
    }
  };
  interface StatisticInfoColumn {
    Column: {
      key: string;
      label: string;
      value: string | number;
      children?: any;
    }[];
  }

  const statisticInfoData: StatisticInfoColumn = {
    Column: [
      {
        key: 'distance',
        label: `${t('ordersPage.statisticInfoDataFields.distanceKm')}(${orderDetails?.distanceUnit})`,
        value: orderDetails?.distance || 'N/A',
      },
      {
        key: 'totalQuantity',
        label: t('ordersPage.statisticInfoDataFields.totalQuantity'),
        value: orderDetails?.totalItems || 'N/A',
      },

      {
        key: 'combinedWeight',
        label: t('ordersPage.statisticInfoDataFields.combinedWeightKg'),
        value: orderDetails?.totalWeight || 'N/A',
      },
      // {
      //   key: 'deliveryWaitTime',
      //   label: t('ordersPage.statisticInfoDataFields.deliveryWaitTime'),
      //   value: '00:00:00',
      //   children: !isOrderLocked && (
      //     <Button
      //       onClick={() => {
      //         setIsFieldModalOpen({
      //           isOpenModal: true,
      //           isOpenTitle: t('ordersPage.statisticInfoDataFields.deliveryWaitTime'),
      //           value: '00:00:00',
      //           type: 'time',
      //           name: 'deliveryWaitTime',
      //         });
      //       }}
      //       className="border-none"
      //       icon={<EditPopupIcon />}
      //     />
      //   ),
      // },
      // {
      //   key: 'pickupWaitTime',
      //   label: t('ordersPage.statisticInfoDataFields.pickupWaitTime'),
      //   value: '00:00:00',
      //   children: !isOrderLocked && (
      //     <Button
      //       onClick={() => {
      //         setIsFieldModalOpen({
      //           isOpenModal: true,
      //           isOpenTitle: t('ordersPage.statisticInfoDataFields.pickupWaitTime'),
      //           value: '00:00:00',
      //           type: 'time',
      //           name: 'pickupWaitTime',
      //         });
      //       }}
      //       className="border-none"
      //       icon={<EditPopupIcon />}
      //     />
      //   ),
      // },
    ],
  };
  const customerInfoData = {
    Column: [
      {
        key: 'company',
        label: t('ordersPage.customerInfoDataFields.company'),
        value: orderDetails?.companyName || 'N/A',
      },
      {
        key: 'customer',
        label: t('ordersPage.customerInfoDataFields.customer'),
        value: orderDetails?.customerName || 'N/A',
      },
      {
        key: 'phone',
        label: t('ordersPage.customerInfoDataFields.phone'),
        value: orderDetails?.customerPhoneNumber || 'N/A',
      },
      {
        key: 'email',
        label: t('ordersPage.customerInfoDataFields.email'),
        value: orderDetails?.customerEmail || 'N/A',
      },
      {
        key: 'contact',
        label: t('ordersPage.customerInfoDataFields.contact'),
        value: orderDetails?.customerContactName || 'N/A',
      },
    ],
  };

  const PhoneNumberAddonBefore = useMemo(
    () => (
      <Form.Item name="countryCode" noStyle initialValue="US">
        <Select
          options={optionsForPrefix}
          placeholder="USA +1"
          onChange={(value) => maskingInputPhone(value)}
        />
      </Form.Item>
    ),
    [maskingInputPhone]
  );

  const renderFieldBasedOnType = (type: string, name: string, value: string) => {
    switch (type) {
      case 'number':
        return (
          <Form.Item name={name} initialValue={value}>
            <InputNumber
              inputMode="decimal"
              formatter={(value) => (value ? `$${value}` : '')}
              maxLength={8}
              className="bulk-adjust-input w-full"
              onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
            />
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item className="w-full max-h-[200px]" name={name}>
            <DatePicker
              use12Hours
              showTime
              hourStep={1}
              defaultValue={value}
              format="DD/MM/YYYY hh:mm A"
              className="orders-general-datepicker w-full h-[40px]"
              suffixIcon={<DateCalendarIcon />}
              showNow={false}
              popupClassName="orders-general-datepicker-dropdown"
            />
          </Form.Item>
        );

      case 'dropdown':
        return (
          <Form.Item name={name} initialValue={value}>
            <Select
              suffixIcon={
                <span className="fields-dropdown-icon">
                  <CollapseUpIcon />
                </span>
              }
              placeholder="Select"
              options={isFieldModalOpen?.options}
              className="w-full h-[40px]"
            />
          </Form.Item>
        );
      case 'isCod':
        return (
          <div className="w-full flex gap-5 pb-10">
            <Form.Item
              layout="vertical"
              label={t('ordersPage.collectAt')}
              className="w-1/2 mb-0"
              name={'codCollectionType'}
              initialValue={orderDetails?.codCollectionType || 'Delivery'}
            >
              <Select
                className="h-[40px]"
                // labelInValue
                suffixIcon={
                  <span className="fields-dropdown-icon">
                    <CollapseUpIcon />
                  </span>
                }
                placeholder="Select"
                options={[
                  { label: 'Pickup', value: 'Pickup' },
                  { label: 'Delivery', value: 'Delivery' },
                ]}
              />
            </Form.Item>
            <Form.Item
              layout="vertical"
              label={t('ordersPage.amountToCollect')}
              className="w-1/2 mb-0"
              name={name}
              initialValue={value}
            >
              <InputNumber
                className="w-full h-[40px] bulk-adjust-input"
                formatter={(value) => (value ? `$${value}` : '')}
                placeholder="$0.00"
                onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
              />
            </Form.Item>
          </div>
        );
      case 'textarea':
        return (
          <Form.Item name={name} initialValue={value}>
            <TextArea rows={4} />
          </Form.Item>
        );
      case 'text':
        return (
          <Form.Item name={name} initialValue={value}>
            <Input maxLength={255} />
          </Form.Item>
        );
      case 'time':
        return (
          <Form.Item name={name} initialValue={convertInUtcToDayjs(value)}>
            <TimePicker use12Hours className="w-full h-[40px]" format={'hh:mm:ss'} />
          </Form.Item>
        );
      case 'masked':
        return (
          <Form.Item name={name}>
            <MaskedInput defaultValue={value} mask={optionsForPrefix[0].mask} />
          </Form.Item>
        );
      case 'email':
        return (
          <Form.Item
            name={name}
            rules={[
              {
                required: true,
                message: 'Please enter email',
              },
              {
                type: 'email',
                message: t('addressPage.operationalForm.emailTypeError'),
              },
            ]}
          >
            <Input type="email" placeholder="Enter email" />
          </Form.Item>
        );
      case 'priceSet':
        return (
          <div className="space-y-4">
            <section className="bg-white max-h-[300px] overflow-y-auto shadow p-0 w-full">
              <span className="block bg-primary-25 h-12 font-semibold p-3  text-base w-full">
                Available Price Sets
              </span>
              <Form.Item name={name} initialValue={value}>
                <Radio.Group
                  onChange={(e) => {
                    const selectedService = availableServices?.data?.find(
                      (service: any) => service.id === e.target.value
                    );
                    if (selectedService) {
                      setSelectedDeliveryService(selectedService);
                      // Initialize modifiers for the selected service
                      if (selectedService.pricing?.modifiers) {
                        const initialModifierStates: { [modifierId: string]: boolean } = {};
                        const selectedModifierIds: string[] = [];

                        selectedService.pricing.modifiers.forEach((modifier: any) => {
                          const isSelected = getInitialModifierState(
                            modifier, 
                            orderDetails?.customPricingSummary, 
                            orderDetails?.priceSetId, 
                            selectedService.id
                          );
                          initialModifierStates[modifier.id] = isSelected;
                          if (isSelected) {
                            selectedModifierIds.push(modifier.id);
                          }
                        });

                        setSelectedModifiers((prev) => ({
                          ...prev,
                          [selectedService.id]: initialModifierStates,
                        }));
                      }
                    }
                  }}
                  className="w-full p-3"
                >
                  <div className="flex flex-col divide-y divide-gray-200 w-full">
                    {!availableServices?.data ? (
                      <div className="p-4 text-center text-gray-500">
                        Loading available services...
                      </div>
                    ) : availableServices.data.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">No services available</div>
                    ) : (
                      availableServices.data.map((service: any) => {
                        const isSelected = selectedDeliveryService?.id === service.id;
                        return (
                          <div key={service.id}>
                            <Radio
                              value={service.id}
                              className={`w-full !m-0`}
                              style={{ width: '100%' }}
                            >
                              <div className="flex justify-between w-full px-4 py-3">
                                <div className="flex flex-col flex-1">
                                  <span className="font-medium text-gray-900">{service.name}</span>
                                  <span className="text-xs text-gray-500">
                                    {expectedDeliveryFormat(service.deliveryDate)}
                                  </span>
                                </div>
                                {service?.pricing && (
                                  <div className="flex flex-col items-end">
                                    <span className="font-semibold text-primary-600">
                                      $
                                      {(() => {
                                        // Calculate the total price based on user's modifier selections
                                        const currentModifierSelections =
                                          selectedModifiers[service.id] || {};
                                        const calculationResult = calculatePriceWithModifiers(
                                          service,
                                          currentModifierSelections
                                        );
                                        return calculationResult.totalAmount.toFixed(2);
                                      })()}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </Radio>
                            {isSelected && service.pricing?.modifiers.length > 0 && (
                              <div className="w-full px-0 pb-4">
                                <div className="bg-white border border-gray-200 rounded-md p-4">
                                  <span className="block font-semibold text-base text-gray-800 mb-3">
                                    Additional services
                                  </span>
                                  <div className="flex flex-col gap-3 max-h-40 overflow-y-auto">
                                    {service.pricing.modifiers.map((modifier: any) => {
                                      const isChecked =
                                        selectedModifiers[service.id]?.[modifier.id] !== undefined
                                          ? selectedModifiers[service.id][modifier.id]
                                          : getInitialModifierState(
                                              modifier, 
                                              orderDetails?.customPricingSummary, 
                                              orderDetails?.priceSetId, 
                                              service.id
                                            );
                                      const isDisabled = isModifierDisabled(modifier);

                                      return (
                                        <label
                                          key={modifier.id}
                                          className={`flex items-center gap-3 ${isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                          tabIndex={0}
                                          aria-label={modifier.name}
                                        >
                                          <Checkbox
                                            className="modifier-checkbox"
                                            checked={isChecked}
                                            disabled={isDisabled}
                                            onChange={(e) =>
                                              handleModifierChange(
                                                service.id,
                                                modifier.id,
                                                e.target.checked
                                              )
                                            }
                                          />
                                          <span
                                            className={`text-sm ${isDisabled ? 'text-gray-500' : 'text-gray-700'}`}
                                          >
                                            {modifier.name}
                                            {modifier.amount > 0 && (
                                              <span className="ml-2 text-xs text-gray-500">
                                                (${modifier.amount.toFixed(2)})
                                              </span>
                                            )}
                                          </span>
                                        </label>
                                      );
                                    })}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })
                    )}
                  </div>
                </Radio.Group>
              </Form.Item>
            </section>
          </div>
        );
      default:
        return;
    }
  };
  const headerForOrderChange = () => (
    <div className="flex flex-col gap-2">
      <div className="flex gap-3">
        <OrdersWarningIcon />
        <span className="text-[16px] font-semibold">{t('ordersPage.orderDetailsChange')}</span>
      </div>
      <p>
        The order total can be updated from $
        {(priceSummary?.pricing?.old?.totalPrice ?? 0).toFixed(2)} to $
        {(priceSummary?.pricing?.new?.totalPrice ?? 0).toFixed(2)} due to pricing changes. Keep
        current prices or update to new ones?
      </p>
    </div>
  );

  const orderGeneralInformation = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {orderInfoData?.Column?.map((item, index) => {
          return (
            <div key={index} className="flex w-full p-1">
              <div className="flex items-center w-2/3 md:gap-4">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && (
                <div className="w-1/3 px-2 flex justify-end items-center">{item.children}</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const orderPriceInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {orderPriceData?.Column?.map((item, index) => {
          return (
            <div key={index} className="flex w-full p-1">
              <div className="flex items-center w-2/3 md:gap-4">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && (
                <div className="w-1/3 px-2 flex justify-end items-center">{item.children}</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };
  const orderStatisticInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {statisticInfoData?.Column?.map((item, index) => {
          return (
            <div key={index} className="flex w-full p-1">
              <div className="flex items-center w-2/3 md:gap-4">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
              {item.children && <div className="w-1/3 px-2 flex justify-end">{item.children}</div>}
            </div>
          );
        })}
      </div>
    );
  };
  const customerOrderInfo = () => {
    return (
      <div className="grid lg:grid-cols-2 w-full">
        {customerInfoData?.Column?.map((item, index) => {
          return (
            <div key={index} className="flex w-full p-1">
              <div className="flex items-center w-2/3 md:gap-4">
                <span className=" w-48 flex items-center gap-1 font-semibold">{item.label}:</span>
                <span>{item.value}</span>
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  const childForOrderChange = () => {
    // Get old and new modifiers
    const oldModifiers = priceSummary?.pricing?.old?.modifiers || [];
    const newModifiers = priceSummary?.pricing?.new?.modifiers || [];

    // Create a map of modifier names to their amounts for easy comparison
    const oldModifierMap = new Map(
      oldModifiers.map((modifier: any) => [modifier.name, modifier.amount])
    );
    const newModifierMap = new Map(
      newModifiers.map((modifier: any) => [modifier.name, modifier.amount])
    );

    // Find changed modifiers
    const changedModifiers: Array<{
      name: string;
      oldAmount: number;
      newAmount: number;
      type: 'added' | 'removed' | 'changed';
    }> = [];

    // Check for removed modifiers
    oldModifiers.forEach((oldModifier: any) => {
      if (!newModifierMap.has(oldModifier.name)) {
        changedModifiers.push({
          name: oldModifier.name,
          oldAmount: oldModifier.amount,
          newAmount: 0,
          type: 'removed',
        });
      }
    });

    // Check for added modifiers
    newModifiers.forEach((newModifier: any) => {
      if (!oldModifierMap.has(newModifier.name)) {
        changedModifiers.push({
          name: newModifier.name,
          oldAmount: 0,
          newAmount: newModifier.amount,
          type: 'added',
        });
      }
    });

    // Check for changed amounts
    newModifiers.forEach((newModifier: any) => {
      const oldAmount = oldModifierMap.get(newModifier.name);
      if (oldAmount !== undefined && oldAmount !== newModifier.amount) {
        changedModifiers.push({
          name: newModifier.name,
          oldAmount: oldAmount as number,
          newAmount: newModifier.amount,
          type: 'changed',
        });
      }
    });

    return (
      <div>
        <ul className="list-disc flex flex-col gap-2 ml-[70px]">
          {changedModifiers.length > 0 ? (
            changedModifiers.map((modifier, index) => {
              const formatAmount = (amount: number) => {
                if (amount === 0) return '$0.00';
                return amount > 0 ? `$${amount.toFixed(2)}` : `($${Math.abs(amount).toFixed(2)})`;
              };

              let changeText = '';
              if (modifier.type === 'added') {
                changeText = `${modifier.name} added: ${formatAmount(modifier.newAmount)}`;
              } else if (modifier.type === 'removed') {
                changeText = `${modifier.name} removed: was ${formatAmount(modifier.oldAmount)}`;
              } else {
                changeText = `${modifier.name} changed from ${formatAmount(modifier.oldAmount)} to ${formatAmount(modifier.newAmount)}`;
              }

              return <li key={index}>{changeText}</li>;
            })
          ) : (
            <li>No specific modifier changes detected</li>
          )}
        </ul>
        <div className="flex flex-row gap-[40px] px-5 py-3">
          <Button
            onClick={async () => {
              await updateOrderHandler({
                payload: {
                  priceSet: selectedDeliveryService?.name,
                  priceSetId: selectedDeliveryService?.id,
                  acceptPriceChanges: true,
                },
                showNotification: true,
              });

              setOpenPanels([]);
            }}
            className="hover:!bg-[#FFFAEB] p-0 bg-[#FFFAEB] border-none"
          >
            <span className="text-primary-500 font-semibold text-[14px]">Accept</span>
          </Button>
          <Button
            onClick={() => {
              setSelectedDeliveryService(null);
              setOpenPanels([]);
            }}
            className="hover:!bg-[#FFFAEB] p-0 bg-[#FFFAEB] border-none"
          >
            <span className="text-primary-500 font-semibold text-[14px]">Dismiss</span>
          </Button>
        </div>
      </div>
    );
  };

  const onPlaceChanged = useCallback(async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);
      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
      };
      form.setFieldsValue(newFormValues);
      try {
        form.resetFields(['zone']);
        if (selectedPlaceData?.postalCode) {
          const trimmedPostalCode = selectedPlaceData?.postalCode.split(' ')[0];
          const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
          form.setFieldValue('zone', response.name);
          await form.validateFields(['zone']);
        } else {
          form.setFields([
            {
              name: 'zone',
              errors: [t('addressPage.operationalForm.noPostalCodeFound')],
            },
          ]);
        }
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === 406007) {
            form.resetFields(['zone']);
            form.setFields([
              {
                name: 'zone',
                errors: [
                  t('addressPage.operationalForm.zoneError', {
                    code: errorStack?.details?.postalCode.split(' ')[0],
                  }),
                ],
              },
            ]);
          }
        }
      }
    }
  }, [form, searchResult, t]);

  const itemForOrderChange =
    priceSummary?.changed > 0 ||
    priceSummary?.priceSetChangedFields ||
    priceSummary?.priceSetChanged ||
    priceSummary?.pricing?.new?.totalPrice !== priceSummary?.pricing?.old?.totalPrice
      ? [
          {
            key: '1',
            label: headerForOrderChange(),
            children: <div className="bg-[#FFFAEB]">{childForOrderChange()}</div>,
          },
        ]
      : [];

  const itemsForOrderDetails = [
    {
      key: 1,
      label: 'General information',
      children: orderGeneralInformation(),
    },
    {
      key: 2,
      label: 'Prices and billing information',
      children: orderPriceInfo(),
    },
    {
      key: 3,
      label: 'Static information',
      children: orderStatisticInfo(),
    },
    {
      key: 4,
      label: 'Customer information',
      children: customerOrderInfo(),
    },
  ];
  const onAddressModalClose = () => {
    setIsOpen({ ...isOpen, isOpenModal: false });
    form.resetFields();
    setIsFormChanged(false);
    setAddressEditType(null);
    setSelectedAddresses({
      pickupAddress: null,
      deliveryAddress: null,
    });
  };

  const onAddressUpdate = async () => {
    const formValues = await form.validateFields();

    // For save, we update the existing address and then update the order
    const addressId =
      formValues.id ||
      (addressEditType === 'pickup'
        ? selectedAddresses.pickupAddress?.id || orderDetails?.collectionAddressId
        : selectedAddresses.deliveryAddress?.id || orderDetails?.deliveryAddressId);

    if (!addressId) {
      throw new Error('Address ID not found');
    }

    // Update the existing address
    await updateCustomerAddressMutation.mutateAsync({
      id: addressId,
      data: {
        ...formValues,
        name: formValues.name,
        countryCode: formValues.countryCode,
        phoneNumber: formValues.phoneNumber,
        phoneExtension: formValues.phoneExtension || null,
        phoneNumberPhoneExtension: formValues.phoneNumberPhoneExtension || null,
        faxNumberPhoneExtension: formValues.faxNumberPhoneExtension || null,
      },
    });

    // Format the address string
    const formattedAddress = [
      formValues.addressLine1,
      formValues.addressLine2,
      formValues.city,
      formValues.province,
      formValues.postalCode,
      formValues.country,
    ]
      .filter(Boolean)
      .join(', ');

    // Get zone information from the form
    const zoneInfo = {
      zoneId: formValues.zoneId || '',
      zoneName: formValues.zone || '',
    };

    // Create payload based on address edit type
    const payload =
      addressEditType === 'pickup'
        ? {
            collectionAddress: formattedAddress,
            collectionCompanyName: formValues.companyName,
            collectionContactName: formValues.name,
            collectionEmail: formValues.email,
            collectionPhone: formValues.phoneNumber,
            collectionPhoneExtension: formValues.phoneExtension || null,
            collectionPhoneNumberPhoneExtension: formValues.phoneNumberPhoneExtension || null,
            collectionFaxNumberPhoneExtension: formValues.faxNumberPhoneExtension || null,
            collectionInstructions: formValues.notes,
            collectionAddressId: addressId,
            collectionZoneId: zoneInfo.zoneId,
            collectionZoneName: zoneInfo.zoneName,
          }
        : {
            deliveryAddress: formattedAddress,
            deliveryCompanyName: formValues.companyName,
            deliveryContactName: formValues.name,
            deliveryEmail: formValues.email,
            deliveryPhone: formValues.phoneNumber,
            deliveryPhoneExtension: formValues.phoneExtension || null,
            deliveryPhoneNumberPhoneExtension: formValues.phoneNumberPhoneExtension || null,
            deliveryFaxNumberPhoneExtension: formValues.faxNumberPhoneExtension || null,
            deliveryInstructions: formValues.notes,
            deliveryAddressId: addressId,
            deliveryZoneId: zoneInfo.zoneId,
            deliveryZoneName: zoneInfo.zoneName,
          };

    await updateOrderHandler({ payload, showNotification: true });

    // Close the modal after successful update
    setIsOpen({ ...isOpen, isOpenModal: false });
    onAddressModalClose();
  };
  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full bg-[#F5F6FF]">
        <div className="flex justify-end gap-3 w-full">
          <Button
            onClick={() => {
              setIsOpen({ ...isOpen, isOpenModal: false });
              setSelectedDeliveryService(null);
              onAddressModalClose();
            }}
            className="border-primary-200 bg-[#FFFFFF] text-[#090A1A] hover:!border-primary-200 hover:!bg-[#FFFFFF] hover:!text-[#090A1A] rounded-lg border-[1px]"
          >
            {t('common.cancel')}
          </Button>
          {isFormChanged === true ? (
            <>
              <Button
                onClick={saveNewAddress}
                className="border-primary-200 bg-[#FFFFFF] text-[#090A1A] hover:!border-primary-200 hover:!bg-[#FFFFFF] hover:!text-[#090A1A] rounded-lg border-[1px]"
              >
                Save as new address
              </Button>
              <Button
                form="edit-location-form"
                loading={
                  updateStatusMutation.isPending ||
                  assignOrderMutation.isPending ||
                  updateOrderMutation.isPending ||
                  createCustomerAddressMutation.isPending ||
                  updateCustomerAddressMutation.isPending
                }
                htmlType="submit"
                type="primary"
                onClick={onAddressUpdate}
                className="bg-primary-500 text-white border-primary-500 rounded-lg hover:!border-primary-500 hover:!text-[white] hover:!bg-primary-500"
              >
                {t('common.save')}
              </Button>
            </>
          ) : (
            <Button
              form="edit-location-form"
              loading={
                updateStatusMutation.isPending ||
                assignOrderMutation.isPending ||
                updateOrderMutation.isPending ||
                createCustomerAddressMutation.isPending ||
                updateCustomerAddressMutation.isPending
              }
              htmlType="submit"
              type="primary"
              onClick={editExistingAddress}
              className="bg-primary-500 text-white border-primary-500 rounded-lg hover:!border-primary-500 hover:!text-[white] hover:!bg-primary-500"
            >
              {t('common.update')}
            </Button>
          )}
        </div>
      </footer>
    ),
    [
      assignOrderMutation.isPending,
      isFormChanged,
      isOpen,
      t,
      updateOrderMutation.isPending,
      updateStatusMutation.isPending,
      createCustomerAddressMutation.isPending,
      updateCustomerAddressMutation.isPending,
    ]
  );

  const FooterForFieldsModal = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full bg-[#F5F6FF]">
        <div className="flex justify-end gap-3 w-full">
          <Button
            onClick={() => setIsFieldModalOpen({ ...isFieldModalOpen, isOpenModal: false })}
            className="border-primary-200 bg-[#FFFFFF] text-[#090A1A] hover:!border-primary-200 hover:!bg-[#FFFFFF] hover:!text-[#090A1A] rounded-lg border-[1px]"
          >
            {t('common.cancel')}
          </Button>
          <Button
            form="fieldsForm"
            loading={updateOrderMutation.isPending}
            htmlType="submit"
            type="primary"
            className="bg-primary-500 text-white border-primary-500 rounded-lg hover:!border-primary-500 hover:!text-[white] hover:!bg-primary-500"
          >
            {t('common.update')}
          </Button>
        </div>
      </footer>
    ),
    [isFieldModalOpen, t, updateOrderMutation.isPending]
  );

  const postalCodeWatcherValue = Form.useWatch('postalCode', form);
  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsOpen({ isOpenModal: false, isOpenTitle: '', isOpenDescription: '' });
          setPreventExit(false);
          setIsBlocked(false);
          setIsFormChanged(false);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsOpen({ isOpenModal: false, isOpenTitle: '', isOpenDescription: '' });
    setIsFormChanged(false);
  }, [isBlocked, setIsBlocked, setPreventExit, t]);

  return (
    <>
      <CustomModal
        className="!w-[500px]"
        style={{ top: '35%' }}
        maskClosable={false}
        destroyOnClose
        onCancel={() => {
          setIsFieldModalOpen({
            ...isFieldModalOpen,
            isOpenModal: false,
            isOpenTitle: '',
            value: '',
            type: '',
            fieldName: '',
            name: '',
            options: undefined,
            onFinish: undefined,
          });
          setIsFormChanged(false);
        }}
        modalTitle={isFieldModalOpen?.isOpenTitle}
        modalDescription=""
        open={isFieldModalOpen?.isOpenModal}
        footer={FooterForFieldsModal}
      >
        <div className="flex flex-col gap-2">
          <span>{isFieldModalOpen?.fieldName ?? isFieldModalOpen?.isOpenTitle}</span>

          <Form
            className="custom-form"
            form={fieldsForm}
            name="fieldsForm"
            onFinish={(values) => {
              // Handle priceSet type specially to send both name and ID
              if (isFieldModalOpen?.type === 'priceSet') {
                const selectedPriceSet = availableServices?.data?.find(
                  (priceSet: any) => priceSet.id === values[isFieldModalOpen.name]
                );

                if (selectedPriceSet) {
                  if (isFieldModalOpen.name === 'serviceLevel') {
                    // For service level, update the order normally
                    const payload = { serviceLevel: selectedPriceSet.name };
                    updateOrderHandler({ payload, showNotification: true });
                  } else {
                    // For price set, calculate modifiers and send calculation result
                    let currentModifierSelections = selectedModifiers[selectedPriceSet.id] || {};

                    // Initialize modifier selections for the selected price set if not exists
                    if (
                      !currentModifierSelections ||
                      Object.keys(currentModifierSelections).length === 0
                    ) {
                      const initialModifierStates: { [modifierId: string]: boolean } = {};
                      selectedPriceSet.pricing?.modifiers?.forEach((modifier: any) => {
                        initialModifierStates[modifier.id] = getInitialModifierState(
                          modifier, 
                          orderDetails?.customPricingSummary, 
                          orderDetails?.priceSetId, 
                          selectedPriceSet.id
                        );
                      });
                      currentModifierSelections = initialModifierStates;
                    }

                    const calculationResult = calculatePriceWithModifiers(
                      selectedPriceSet,
                      currentModifierSelections
                    );

                    const payload = {
                      priceSet: selectedPriceSet.name,
                      priceSetId: selectedPriceSet.id,
                      calculationResult: {
                        priceSetId: selectedPriceSet.id,
                        selectedModifiers: calculationResult.selectedModifiers,
                        modifiersTotal: calculationResult.modifiersTotal,
                        disSelectedModifiers: calculationResult.disSelectedModifiers,
                        basePrice: calculationResult.basePrice,
                      },
                    };
                    updateOrderHandler({ payload, showNotification: true });
                  }
                }
              } else {
                updateOrderHandler({ payload: values, showNotification: true });
              }
            }}
            preserve={false}
          >
            {renderFieldBasedOnType(
              isFieldModalOpen?.type,
              isFieldModalOpen.name,
              isFieldModalOpen.value
            )}
          </Form>
        </div>
      </CustomModal>
      <CustomModal
        maskClosable={false}
        destroyOnClose
        onCancel={closeModalHandler}
        footer={Footer}
        open={isOpen.isOpenModal}
        modalTitle={isOpen.isOpenTitle}
        modalDescription={isOpen.isOpenDescription}
      >
        <Form
          name="edit-location-form"
          layout="vertical"
          className="custom-form"
          form={form}
          onFinishFailed={(errors) => {
            form.scrollToField(errors.errorFields[0].name, { behavior: 'smooth' });
          }}
          onFieldsChange={(changedFields) => {
            const relevantChanges = changedFields.filter((field) => field.name !== 'addressSelect');
            const hasChanges = relevantChanges.length > 0;
            setIsFormChanged(hasChanges);
          }}
          onChangeCapture={undefined}
          preserve={false}
        >
          <CustomDivider label={t('common.divider.basicDetails')} />
          <div className="form-fields-wrapper flex gap-2.5 flex-col">
            <Form.Item name={'addressSelect'} label="Search Location" className="w-6/6 mb-0">
              <Select
                onSearch={(value) => {
                  setAllAddresses(
                    allAddresses?.filter((address) =>
                      address.name.toLowerCase().includes(value.toLowerCase())
                    ) as GetAddressDto[]
                  );
                }}
                showSearch
                onSelect={(value) => {
                  setAllAddresses(
                    allAddresses?.filter((address) =>
                      address.name.toLowerCase().includes(value.toLowerCase())
                    ) as GetAddressDto[]
                  );
                }}
                open={isOpenForAddress.pickUpAddress}
                placeholder="Search Contact, Company or Address"
                className="w-full rounded-lg h-[40px]"
                aria-label="Search location"
                tabIndex={0}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  typeof option?.children === 'string' &&
                  (option.children as string).toLowerCase().includes(input.toLowerCase())
                }
                onDropdownVisibleChange={(value) =>
                  setIsOpenForAddress({ ...isOpenForAddress, pickUpAddress: value })
                }
                dropdownRender={() => {
                  return (
                    <div className="max-h-[300px] overflow-y-auto">
                      {allAddresses?.length > 0 && (
                        <div className="py-2 px-3">
                          <span className="font-bold text-sm text-primary-600">All Address</span>
                          {allAddresses?.map((address) => {
                            const isSelected = selectedAddresses.pickupAddress?.id === address.id;
                            return (
                              <div
                                key={address.id}
                                className={`flex justify-between items-center px-3 py-2 hover:bg-gray-100 cursor-pointer ${isSelected ? 'bg-primary-25 text-primary-600' : ''}`}
                                onClick={() => handleSelect(address, 'pickupAddress')}
                                tabIndex={0}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' || e.key === ' ') {
                                    handleSelect(address, 'pickupAddress');
                                  }
                                }}
                              >
                                <span>
                                  {address.name} ({address.companyName})
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      )}
                      {!allAddresses ||
                        (allAddresses?.length === 0 && (
                          <span className="h-[35px] mx-2">
                            No Addresses are available to assign
                          </span>
                        ))}
                    </div>
                  );
                }}
              ></Select>
            </Form.Item>
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.name')}
                  </span>
                }
                validateFirst
                name="name"
                rules={[
                  { required: true, message: t('addressPage.operationalForm.nameError') },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.namePlaceholder')}
                  maxLength={255}
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.companyName')}
                  </span>
                }
                name="companyName"
                validateFirst
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.companyNameError'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.companyNamePlaceholder')}
                  maxLength={255}
                />
              </Form.Item>
            </div>

            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.email')}
                  </span>
                }
                name="email"
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.emailError'),
                  },
                  { type: 'email', message: t('addressPage.operationalForm.emailTypeError') },
                ]}
              >
                <Input
                  placeholder={t('addressPage.operationalForm.emailPlaceholder')}
                  maxLength={255}
                />
              </Form.Item>

              <Space.Compact className="combined-input">
                <Form.Item
                  dependencies={['countryCode']}
                  validateFirst
                  className="w-[75%]"
                  rules={[
                    {
                      required: true,
                      validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                    },
                    {
                      validator: (_, value) =>
                        validateMaskedInput(
                          value,
                          maskPhoneInput.length,
                          t('addressPage.operationalForm.validPhoneNumberError')
                        ),
                    },
                  ]}
                  name="phoneNumber"
                  label={
                    <span className="font-semibold text-[#20363F]">
                      {t('addressPage.operationalForm.phoneNumber')}
                    </span>
                  }
                >
                  <MaskedInput
                    ref={inputPhoneRef}
                    addonBefore={PhoneNumberAddonBefore}
                    className="customer-general-maskedInput address-popup-maskedInput"
                    placeholder={t('addressPage.operationalForm.phoneNumberPlaceholder')}
                    mask={maskPhoneInput.mask}
                    onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
                  />
                </Form.Item>
                <Form.Item name="phoneExtension" className="w-[25%]">
                  <Input
                    placeholder={t('addressPage.operationalForm.phoneExtPlaceholder')}
                    maxLength={6}
                    onKeyDown={(event) => numberFieldValidator(event, {})}
                  />
                </Form.Item>
              </Space.Compact>
            </div>
            <CustomDivider label={t('addressPage.operationalForm.locationDividerText')} />
            <CustomGoogleAutoComplete
              onLoad={onLoad}
              onPlaceChanged={onPlaceChanged}
              ref={autocompleteRef}
            >
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.addressLine1')}
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('addressPage.operationalForm.addressLine1Error'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
                name="addressLine1"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.addressLine1Placeholder')}
                  maxLength={255}
                  id="autoComplete"
                />
              </Form.Item>
            </CustomGoogleAutoComplete>

            <Form.Item
              label={
                <span className="font-semibold text-[#20363F]">
                  {t('addressPage.operationalForm.addressLine2')}
                </span>
              }
              name="addressLine2"
              rules={[
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
              ]}
            >
              <Input
                placeholder={t('addressPage.operationalForm.addressLine2Placeholder')}
                maxLength={255}
              />
            </Form.Item>
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.city')}
                  </span>
                }
                rules={[{ required: true, message: t('addressPage.operationalForm.cityError') }]}
                name="city"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.cityPlaceholder')}
                  maxLength={255}
                  disabled
                />
              </Form.Item>

              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.province')}
                  </span>
                }
                rules={[
                  { required: true, message: t('addressPage.operationalForm.provinceError') },
                ]}
                name="province"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.provincePlaceholder')}
                  maxLength={100}
                  disabled
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.postalCode')}
                  </span>
                }
                validateFirst
                rules={[
                  { required: true, message: t('addressPage.operationalForm.postalCodeError') },
                  {
                    pattern: formErrorRegex.PostalCode,
                    message: t('addressPage.operationalForm.validPostalCodeError'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
                name="postalCode"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.postalCodePlaceholder')}
                  maxLength={20}
                  disabled
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.country')}
                  </span>
                }
                rules={[{ required: true, message: t('addressPage.operationalForm.countryError') }]}
                name="country"
              >
                <Input
                  placeholder={t('addressPage.operationalForm.countryPlaceholder')}
                  maxLength={100}
                  disabled
                />
              </Form.Item>
            </div>

            <Form.Item
              label={
                <span className="flex gap-1 font-semibold text-[#20363F]">
                  {t('addressPage.operationalForm.zone')}
                  <CustomTooltip title={t('customerAddressPage.operationalForm.zoneToolTip')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                </span>
              }
              validateFirst
              rules={[
                {
                  required: true,
                  message: postalCodeWatcherValue
                    ? t('addressPage.operationalForm.zoneError', {
                        code: postalCodeWatcherValue.split(' ')[0],
                      })
                    : t('addressPage.operationalForm.noPostalCodeFound'),
                },
              ]}
              name="zone"
              dependencies={['addressLine1']}
            >
              <Input
                placeholder={`${t('addressPage.operationalForm.zonePlaceholder')}`}
                maxLength={50}
                disabled
              />
            </Form.Item>

            {/* Hidden field to store zone ID */}
            <Form.Item name="zoneId" hidden>
              <Input />
            </Form.Item>

            {/* Hidden field to store address ID */}
            <Form.Item name="id" hidden>
              <Input />
            </Form.Item>

            <CustomDivider label={t('addressPage.operationalForm.commentsDividerTex')} />

            <Form.Item
              label={
                <>
                  <CustomTooltip title={t('customerAddressPage.operationalForm.notesTooltip')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </CustomTooltip>
                  <span className="font-semibold text-[#20363F]">
                    {t('addressPage.operationalForm.comments')}
                  </span>
                </>
              }
              name="notes"
            >
              <TextArea
                placeholder={t('addressPage.operationalForm.commentsPlaceHolder')}
                maxLength={255}
                style={{
                  minHeight: 54,
                  maxHeight: 100,
                }}
              />
            </Form.Item>
          </div>
        </Form>
        {/* <Form form={form}
        >
         
        </Form> */}
      </CustomModal>
      <div className="py-5 pr-5 flex flex-col w-[100%] gap-5 max-h-[84.5vh] overflow-y-auto">
        {itemForOrderChange.length > 0 && (
          <Collapse
            expandIcon={({ isActive }) => {
              return (
                <span className="text-primary-500 font-semibold text-[14px]">
                  {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
                </span>
              );
            }}
            activeKey={openPanels}
            collapsible="header"
            onChange={(key) => {
              setOpenPanels(key as string[]);
            }}
            defaultActiveKey={'1'}
            className="order-changes-collapse bg-[#FFFAEB] !border-[1px] !border-[#F79009] rounded-[8px]"
            items={itemForOrderChange}
            expandIconPosition="end"
            accordion
          />
        )}
        <OrderStatusSteps orderDetails={orderDetails as any} />

        <CustomDivider label={t('ordersPage.orderDetails')} />
        <div className="flex w-full sm:flex-col lg:flex-row gap-3">
          <Card className="sm:w-full lg:w-1/2 bg-[#F5F6FF] border-none rounded-sm">
            <div className="flex flex-col gap-3">
              <div className="flex w-full justify-between ">
                <div className="flex gap-2 justify-center align-middle items-center">
                  <span className="text-[14px] font-semibold">
                    {t('ordersPage.collectionLocation')}
                  </span>
                  <span className="bg-[#12B76A] text-white px-2 py-[2px] rounded-md text-[12px]">
                    {orderDetails?.collectionZoneName || 'N/A'}{' '}
                  </span>
                </div>
                {!isOrderLocked && (
                  <Button
                    onClick={() => {
                      setAddressEditType('pickup');
                      setIsOpen({
                        isOpenModal: true,
                        isOpenTitle: t('ordersPage.collectionLocation'),
                        isOpenDescription: t('ordersPage.enterCollectionLocationDetails'),
                      });
                      const pickupFormValues = {
                        name: orderDetails?.collectionContactName || '',
                        companyName: orderDetails?.collectionCompanyName || '',
                        addressLine1: orderDetails?.collectionAddressLine1 || '',
                        addressLine2: orderDetails?.collectionAddressLine2 || '',
                        city: orderDetails?.collectionCity || '',
                        province: orderDetails?.collectionProvince || '',
                        postalCode: orderDetails?.collectionPostalCode || '',
                        country: orderDetails?.collectionCountry || '',
                        email: orderDetails?.collectionEmail || '',
                        phoneNumber: orderDetails?.collectionPhoneNumber || '',
                        phoneExtension: orderDetails?.collectionPhoneExtension || '',
                        notes: orderDetails?.collectionInstructions || '',
                        zone: orderDetails?.collectionZoneName || '',
                        zoneId: orderDetails?.collectionZoneId || '',
                        countryCode: 'US', // Default country code
                        id: orderDetails?.collectionAddressId || '',
                      };
                      form.setFieldsValue(pickupFormValues);
                      setIsFormChanged(false);
                      maskingInputPhone(pickupFormValues.countryCode, false);
                    }}
                    icon={<EditPopupIcon />}
                    className="cursor-pointer bg-[#F5F6FF] border-none"
                  />
                )}
              </div>
              <div className="flex flex-col gap-[3px]">
                <span className="text-[14px] font-semibold">
                  {orderDetails?.collectionCompanyName || 'N/A'}
                </span>
                <span className="text-[12px] font-semibold">
                  {orderDetails?.collectionContactName || 'N/A'}
                </span>
              </div>
              <div className="flex gap-2">
                <span className="w-[20px]">
                  <LocationOutlinedIcon />
                </span>
                <span className="text-[14px] font-semibold">
                  {orderDetails?.collectionAddressLine1}
                  {orderDetails?.collectionAddressLine2
                    ? `, ${orderDetails.collectionAddressLine2}`
                    : ''}
                  {orderDetails?.collectionCity ? `, ${orderDetails.collectionCity}` : ''}
                  {orderDetails?.collectionProvince ? `, ${orderDetails.collectionProvince}` : ''}
                  {orderDetails?.collectionPostalCode
                    ? `, ${orderDetails.collectionPostalCode}`
                    : ''}
                  {orderDetails?.collectionCountry ? `, ${orderDetails.collectionCountry}` : ''}
                </span>
              </div>
            </div>
          </Card>
          <Card className="sm:w-full lg:w-1/2 bg-[#F5F6FF] border-none rounded-sm">
            <div className="flex flex-col gap-3">
              <div className="flex w-full justify-between ">
                <div className="flex gap-2 justify-center align-middle items-center">
                  <span className="text-[14px] font-semibold">
                    {t('ordersPage.deliveryLocation')}
                  </span>
                  <span className="bg-[#12B76A] text-white px-2 py-[2px] rounded-md text-[12px]">
                    {orderDetails?.deliveryZoneName || 'N/A'}
                  </span>
                </div>
                {!isOrderLocked && (
                  <Button
                    icon={<EditPopupIcon />}
                    onClick={() => {
                      setAddressEditType('delivery');
                      setIsOpen({
                        isOpenModal: true,
                        isOpenTitle: t('ordersPage.deliveryLocation'),
                        isOpenDescription: t('ordersPage.enterDeliveryLocationDetails'),
                      });
                      const deliveryFormValues = {
                        name: orderDetails?.deliveryContactName || '',
                        companyName: orderDetails?.deliveryCompanyName || '',
                        addressLine1: orderDetails?.deliveryAddressLine1 || '',
                        addressLine2: orderDetails?.deliveryAddressLine2 || '',
                        city: orderDetails?.deliveryCity || '',
                        province: orderDetails?.deliveryProvince || '',
                        postalCode: orderDetails?.deliveryPostalCode || '',
                        country: orderDetails?.deliveryCountry || '',
                        email: orderDetails?.deliveryEmail || '',
                        phoneNumber: orderDetails?.deliveryPhoneNumber || '',
                        phoneExtension: orderDetails?.deliveryPhoneExtension || '',
                        notes: orderDetails?.deliveryInstructions || '',
                        zone: orderDetails?.deliveryZoneName || '',
                        zoneId: orderDetails?.deliveryZoneId || '',
                        countryCode: 'CAN',
                        id: orderDetails?.deliveryAddressId || '',
                      };
                      form.setFieldsValue(deliveryFormValues);
                      setIsFormChanged(false);
                      maskingInputPhone(deliveryFormValues.countryCode, false);
                    }}
                    className="cursor-pointer bg-[#F5F6FF] border-none"
                  />
                )}
              </div>
              <div className="flex flex-col gap-[3px]">
                <span className="text-[14px] font-semibold">
                  {orderDetails?.deliveryCompanyName || 'N/A'}
                </span>
                <span className="text-[12px] font-semibold">
                  {' '}
                  {orderDetails?.deliveryContactName}
                </span>
              </div>
              <div className="flex gap-2">
                <span className="w-[20px]">
                  <LocationOutlinedIcon />
                </span>
                <span className="text-[14px] font-semibold text-wrap">
                  {orderDetails?.deliveryAddressLine1}
                  {orderDetails?.deliveryAddressLine2
                    ? `, ${orderDetails.deliveryAddressLine2}`
                    : ''}
                  {orderDetails?.deliveryCity ? `, ${orderDetails.deliveryCity}` : ''}
                  {orderDetails?.deliveryProvince ? `, ${orderDetails.deliveryProvince}` : ''}
                  {orderDetails?.deliveryPostalCode ? `, ${orderDetails.deliveryPostalCode}` : ''}
                  {orderDetails?.deliveryCountry ? `, ${orderDetails.deliveryCountry}` : ''}
                </span>
              </div>
            </div>
          </Card>
        </div>
        <Collapse
          expandIcon={({ isActive }) => {
            return (
              <span className="text-primary-500 font-semibold text-[14px]">
                {isActive ? <CollapseDownIcon /> : <CollapseUpIcon />}
              </span>
            );
          }}
          defaultActiveKey={['1', '2', '3', '4']}
          collapsible="header"
          items={itemsForOrderDetails}
          className="order-details-collapse"
          bordered={false}
          expandIconPosition="end"
        ></Collapse>
      </div>
    </>
  );
};
export default OrdersGeneralComponent;
