import { GetTimeClockSessionDto } from '@/api/vehicle/timeClockSession/timeClockSession.types';
import { GetVehicleDto } from '@/api/vehicle/vehicle.types';
import { FormInstance } from 'antd';
import dayjs from 'dayjs';

export interface IVehicle {
  id?: string;
  type: string;
  capacity: number;
  fleetId: string;
  odometer: string;
  license: string;
  branches: string;
  capabilities: string;
  ownedBy: string;
  packageType: string[];
  make: string;
  model: string;
  year: string;
  defaultDriver: string;
  comment: string;
  VIN: string;
  timeClockSessions: ITimeClockSessions[];
}

export interface ITimeClockSessions {
  id?: string;
  entryDate: string;
  driverName: string;
  distance: number;
  startTime: string;
  endTime: string;
  vehicle: string;
  totalDistance?: number;
  totalTime?: string;
  source: string;
}
export interface ITimeClockSessionsForm {
  id?: string;
  driverId: string;
  distanceTraveled: number;
  dateTime: [dayjs.Dayjs, dayjs.Dayjs];
  vehicle: string;
  totalDistance?: number;
  totalTime?: string;
  showTotalTimeWarning?: boolean;
}

export interface IVehicleTimeClockSessionsProps {
  currentVehicleDetails: GetVehicleDto;
  getVehicle: () => Promise<void>;
}

export interface IVehicleTimeClockSessionsOperationFormProps {
  form: FormInstance<ITimeClockSessionsForm>;
  onFinish: (formValues: ITimeClockSessionsForm) => Promise<void>;
  isOpenModel: {
    isOpen: boolean;
    isEdit: boolean;
    session: GetTimeClockSessionDto | null;
  };
}
export interface IVehicleGeneral {
  form: FormInstance<GetVehicleDto>;
  onFinish: (formValues: GetVehicleDto) => any;
  currentVehicleDetails: GetVehicleDto;
  isButtonLoading: boolean;
}
