import { DeleteIcon, EyeIcon, PlusButtonIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomModal from '@/components/common/modal/CustomModal';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import { GridNames } from '@/types/AppEvents';
import Icon from '@ant-design/icons';
import { Button, Divider, Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import VehicleTimeClockSessionsOperationForm from './timeClockSessionsOperation';
import { ICellRendererParams } from 'ag-grid-community';
import utc from 'dayjs/plugin/utc';
import { calculateTime, getPaginationData, searchData } from '@/lib/helper';
import { ITimeClockSessionsForm, IVehicleTimeClockSessionsProps } from '../../vehicleTypes';
import { useParams } from 'react-router-dom';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { useConfig } from '@/contexts/ConfigContext';
import { useLanguage } from '@/hooks/useLanguage';
import { AgGridReact } from 'ag-grid-react';
import { on } from '@/contexts/PulseContext';
import { dateFormatter } from '@/lib/helper/dateHelper';
import {
  CreateTimeClockSessionDto,
  GetTimeClockSessionDto,
  SessionSource,
  SessionStatus,
} from '@/api/vehicle/timeClockSession/timeClockSession.types';
import { timeClockSessionServiceHook } from '@/api/vehicle/timeClockSession/useTimeClockSession';
import useThrottle from '@/hooks/useThrottle';
import { defaultPagination } from '@/constant/generalConstant';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { useNavigationContext } from '@/hooks/useNavigationContext';

dayjs.extend(utc);

const VehicleTimeClockSessions: React.FC<IVehicleTimeClockSessionsProps> = (props): JSX.Element => {
  const { currentVehicleDetails } = props;
  const [timeClockSessionsState, setTimeClockSessionsState] = useState<GetTimeClockSessionDto[]>();
  const [sessionsFilter, setSessionsFilter] = useState<GetTimeClockSessionDto[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isModalOpen, setIsModalOpen] = useState<{
    isOpen: boolean;
    isEdit: boolean;
    session: GetTimeClockSessionDto | null;
  }>({
    isOpen: false,
    isEdit: false,
    session: null,
  });
  const { id: vehicleId } = useParams();
  const notificationManager = useNotificationManager();
  const { config } = useConfig();
  const { t } = useLanguage();
  const gridRef = useRef<AgGridReact>(null);
  const [vehicleTimeClockSessionsForm] = Form.useForm<ITimeClockSessionsForm>();
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);

  const {
    data: timeClockSessions,
    refetch: refetchSessions,
    isLoading,
    isFetching,
  } = timeClockSessionServiceHook.useEntities(`vehicle/${vehicleId}`, filterParams, {
    enabled: Boolean(vehicleId),
  });

  useEffect(() => {
    if (timeClockSessions && !isFetching) {
      setTimeClockSessionsState(timeClockSessions?.data);
      setSessionsFilter(timeClockSessions?.data || []);
    }
  }, [isFetching, timeClockSessions]);

  const deleteMutation = timeClockSessionServiceHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.sessionSuccessDelete'),
      });
      await refetchSessions();
    },
    onError: () => {
      return notificationManager.error({
        message: t('common.error'),
        description: t('vehiclePage.notificationMessages.failedSessionDelete'),
      });
    },
  });

  const createMutation = timeClockSessionServiceHook.useCreate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.sessionSuccessAdded'),
      });
      await refetchSessions();
    },
  });

  const updateMutation = timeClockSessionServiceHook.useUpdate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.sessionSuccessUpdate'),
      });
      await refetchSessions();
    },
  });

  const fillTimeClockSessionFormHandler = useCallback(
    (valuesToFill: GetTimeClockSessionDto) => {
      setIsModalOpen({ isOpen: true, isEdit: true, session: valuesToFill });

      const isEndDateIsNotDate = Boolean(!valuesToFill.endTime);
      const endDateAndTime = isEndDateIsNotDate ? undefined : dayjs(valuesToFill.endTime);
      const dateAndTimeObject = calculateTime(dayjs(valuesToFill.startTime), dayjs(endDateAndTime));

      vehicleTimeClockSessionsForm.setFieldsValue({
        ...valuesToFill,
        dateTime: [dayjs(valuesToFill.startTime), endDateAndTime as Dayjs],
        totalTime: dateAndTimeObject.totalMinutes,
        showTotalTimeWarning: isEndDateIsNotDate,
      });
    },
    [vehicleTimeClockSessionsForm]
  );

  const deleteSessionHandler = useCallback(
    async (sessionId: string) => {
      await deleteMutation.mutateAsync(sessionId as string);
      customAlert.destroy();
    },
    [deleteMutation]
  );

  const deleteTimeClockSessionConfirmation = useCallback(
    (sessionId: string) => {
      customAlert.error({
        title: t('vehiclePage.alert.sessionDeleteConfirmation'),
        message: t('vehiclePage.alert.sessionDeleteConfirmationMessage'),
        secondButtonFunction: () => customAlert.destroy(),
        firstButtonFunction: () => deleteSessionHandler(sessionId),
        secondButtonTitle: t('vehiclePage.alert.secondBtnText'),
        firstButtonTitle: t('vehiclePage.alert.firstBtnText'),
      });
    },
    [deleteSessionHandler, t]
  );

  const endDateAndTimeCellRenderer = useCallback(
    (params: ICellRendererParams<GetTimeClockSessionDto>) => {
      const { value } = params;
      if (!params.data?.endTime) {
        const isActive = params.data?.status === SessionStatus.ACTIVE;
        return (
          <span
            className={`px-2 py-[2px] border rounded-md ${
              isActive
                ? 'text-success-600 bg-success-50 border-success-600'
                : 'text-error-600 bg-error-50 border-error-600'
            }`}
          >
            {isActive ? t('vehiclePage.activeSession') : t('vehiclePage.inactiveSession')}
          </span>
        );
      }

      const formattedDate = value ? dateFormatter(value) : '-';
      return searchText ? highlightText(formattedDate, searchText) : formattedDate;
    },
    [searchText, t]
  );

  const totalTimeCellRenderer = useCallback(
    (params: ICellRendererParams<GetTimeClockSessionDto>) => {
      const { data } = params;
      const endTime = data?.endTime;
      const startTime = data?.startTime;

      const totalTimeCellValue = !endTime
        ? ''
        : calculateTime(dayjs(startTime).utc(), dayjs(endTime).utc()).totalMinutes;

      return searchText ? highlightText(totalTimeCellValue, searchText) : totalTimeCellValue;
    },
    [searchText]
  );

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.timeClockSession.sortable.includes(field);
  }, []);

  const timeClockSessionsColDefs: IColDef[] = useMemo(
    () => [
      {
        field: 'createdAt',
        headerName: t('vehiclePage.colDefs.entryDate'),
        visible: true,
        minWidth: 200,
        type: 'date',
        flex: 1,
        sortable: isColumnSortable('createdAt'),
        unSortIcon: isColumnSortable('createdAt'),
        cellRenderer: (params: { value: string }) => {
          const formattedValue = dateFormatter(params.value, config.dateFormateWithoutTime);
          return searchText ? highlightText(formattedValue, searchText) : formattedValue;
        },
      },
      {
        field: 'driver',
        headerName: t('vehiclePage.timeClockColDefs.driverName'),
        sortable: isColumnSortable('driverName'),
        unSortIcon: isColumnSortable('driverName'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'string',
        cellRenderer: (params: { value: string }) => {
          return params.value;
        },
      },
      {
        field: 'distanceTraveled',
        headerName: `${t('vehiclePage.timeClockColDefs.distance')} (${config.units?.distance})`,
        sortable: isColumnSortable('distanceTraveled'),
        unSortIcon: isColumnSortable('distanceTraveled'),
        visible: true,
        minWidth: 200,
        flex: 1,
        type: 'number',
        cellRenderer: (params: { value: string }) => {
          const value = params?.value?.toString() || '';
          return searchText ? highlightText(value, searchText) : value;
        },
      },
      {
        field: 'startTime',
        headerName: t('vehiclePage.timeClockColDefs.startTime'),
        sortable: isColumnSortable('startTime'),
        unSortIcon: isColumnSortable('startTime'),
        visible: true,
        minWidth: 200,
        type: 'date',
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          const formattedValue = dateFormatter(params.value);
          return searchText ? highlightText(formattedValue, searchText) : formattedValue;
        },
      },
      {
        field: 'endTime',
        headerName: t('vehiclePage.timeClockColDefs.endTime'),
        sortable: isColumnSortable('endTime'),
        unSortIcon: isColumnSortable('endTime'),
        visible: true,
        minWidth: 200,
        type: 'date',
        flex: 1,
        cellRenderer: endDateAndTimeCellRenderer,
      },
      {
        field: 'totalHours',
        headerName: t('vehiclePage.timeClockColDefs.totalTime'),
        sortable: isColumnSortable('totalHours'),
        unSortIcon: isColumnSortable('totalHours'),
        visible: true,
        minWidth: 200,
        type: 'date',
        flex: 1,
        cellRenderer: totalTimeCellRenderer,
      },
      {
        field: 'source',
        headerName: 'Source',
        sortable: isColumnSortable('source'),
        unSortIcon: isColumnSortable('source'),
        visible: true,
        minWidth: 200,
        type: 'string',
        flex: 1,
        cellRenderer: (params: { value: SessionSource }) => {
          const sourceValue =
            params.value === SessionSource.MANUAL
              ? t('vehiclePage.timeClockSessionForm.manual')
              : t('vehiclePage.timeClockSessionForm.automatic');
          return searchText ? highlightText(sourceValue, searchText) : sourceValue;
        },
      },
      {
        field: 'action',
        headerName: t('vehiclePage.timeClockColDefs.action'),
        pinned: 'right',
        width: 85,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<GetTimeClockSessionDto>) => {
          return (
            <div className="flex gap-2 h-full items-center w-full overflow-hidden">
              <Icon
                component={EyeIcon}
                className="cursor-pointer"
                onClick={() =>
                  fillTimeClockSessionFormHandler(params.data as GetTimeClockSessionDto)
                }
              />
              <Icon
                component={DeleteIcon}
                className="cursor-pointer"
                onClick={() => deleteTimeClockSessionConfirmation(params.data?.id as string)}
              />
            </div>
          );
        },
        visible: true,
      },
    ],
    [
      config.dateFormateWithoutTime,
      config.units?.distance,
      deleteTimeClockSessionConfirmation,
      endDateAndTimeCellRenderer,
      fillTimeClockSessionFormHandler,
      isColumnSortable,
      searchText,
      t,
      totalTimeCellRenderer,
    ]
  );

  const searchHandler = useCallback(
    (value: string) => {
      const results = searchData(
        sessionsFilter,
        {
          query: value,
        },
        timeClockSessionsColDefs
      );
      setSearchText(value);
      setTimeClockSessionsState(results);
    },
    [timeClockSessionsColDefs, sessionsFilter]
  );
  const { isBlocked, setIsBlocked } = useNavigationContext();

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsModalOpen({ isOpen: false, isEdit: false, session: null });
          vehicleTimeClockSessionsForm.resetFields();
          setIsBlocked(false);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsModalOpen({ isOpen: false, isEdit: false, session: null });
    vehicleTimeClockSessionsForm.resetFields();
  }, [isBlocked, setIsBlocked, t, vehicleTimeClockSessionsForm]);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="custom-antd-outlined" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="time-clock-sessions"
          htmlType="submit"
          loading={isLoading || isFetching}
          type="primary"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {isModalOpen.isEdit ? t('common.update') : t('common.save')}
        </Button>
      </footer>
    ),
    [closeModalHandler, isFetching, isLoading, isModalOpen.isEdit, t]
  );

  const onFinishHandler = useThrottle(async (formValues: ITimeClockSessionsForm) => {
    const sessionMutationPayload: CreateTimeClockSessionDto = {
      driverId: formValues.driverId,
      vehicleId: vehicleId as string,
      startTime: formValues.dateTime[0].format(),
      endTime: formValues.dateTime[1].format(),
      distanceTraveled: Number(formValues.distanceTraveled),
      source: SessionSource.MANUAL,
    };
    if (isModalOpen.isEdit && isModalOpen.session) {
      await updateMutation.mutateAsync({
        id: isModalOpen.session.id,
        data: sessionMutationPayload,
      });
    } else {
      await createMutation.mutateAsync(sessionMutationPayload);
    }
    closeModalHandler();
  }, 3000);

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.timeClockSessionsGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);
  const paginationData = useMemo(() => getPaginationData(timeClockSessions), [timeClockSessions]);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  return (
    <>
      <CustomModal
        modalTitle={
          isModalOpen.isEdit
            ? t('vehiclePage.modal.editSession', {
                name: `${currentVehicleDetails.model}(${currentVehicleDetails.fleetId})`,
              })
            : t('vehiclePage.addNameSession', {
                name: `${currentVehicleDetails.model}(${currentVehicleDetails.fleetId})`,
              })
        }
        modalDescription={
          isModalOpen.isEdit
            ? t('vehiclePage.modal.headerDescEdit')
            : t('vehiclePage.modal.headerDesc')
        }
        open={isModalOpen.isOpen}
        onCancel={closeModalHandler}
        maskClosable={false}
        footer={Footer}
        destroyOnClose
        keyboard={false}
        onClose={() => vehicleTimeClockSessionsForm.resetFields()}
      >
        <VehicleTimeClockSessionsOperationForm
          form={vehicleTimeClockSessionsForm}
          onFinish={onFinishHandler}
          isOpenModel={isModalOpen}
        />
      </CustomModal>
      <div className="py-4 h-[80vh]">
        <header className="flex justify-between items-center gap-3">
          <div className="bg-primary-25 w-fit rounded p-2 font-semibold text-primary-500">
            {t('vehiclePage.form.totalDistance')} - {timeClockSessions?.totalDistance || 0}{' '}
            {config.units?.distance}
          </div>
          <div className="flex items-center gap-3">
            <div className="flex gap-3">
              <SearchFilterComponent
                colDefs={timeClockSessionsColDefs}
                isSetQuickFilter={false}
                onFilterApply={applyFilters}
                advanceFilter
                className="!pt-0"
                searchInputPlaceholder={t('vehiclePage.header.searchSession')}
                onSearch={triggerSearch}
                setSelectedQuickFilterData={setSelectedQuickFilterData}
                supportedFields={filterableModules.timeClockSession.advanceFilter}
                clearAllFunctionRef={clearAllFunctionRef}
                setFilterParams={setFilterParams}
              />
            </div>
            <ColumnManage
              gridName={GridNames.timeClockSessionsGrid}
              colDefs={timeClockSessionsColDefs}
              className="pt-0"
            />
            <Divider type="vertical" className="h-[40px] !m-0" />
            <Button
              form="time-clock-sessions"
              htmlType="submit"
              type="primary"
              icon={<PlusButtonIcon />}
              onClick={() => setIsModalOpen({ isOpen: true, isEdit: false, session: null })}
              className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600 h-[40px] lg:px-6"
            >
              {t('vehiclePage.header.addNewTimeClockSessionBtnText')}
            </Button>
          </div>
        </header>
        <ActiveFilters
          selectedQuickFilterData={selectedQuickFilterData}
          clearAllToDefault={clearAllToDefault}
          colDefs={timeClockSessionsColDefs}
        />
        <main className="overflow-x-hidden overflow-y-auto bg-white mt-4">
          <CustomAgGrid
            gridRef={gridRef}
            loading={isLoading || isFetching}
            rowData={timeClockSessionsState}
            gridName={GridNames.timeClockSessionsGrid}
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
            columnDefs={timeClockSessionsColDefs}
            className="!h-[70vh] lg:!h-[71vh]"
            onCellClicked={(params) =>
              params.colDef.field !== 'action' &&
              fillTimeClockSessionFormHandler(params.data as GetTimeClockSessionDto)
            }
            paginationProps={{
              ...paginationData,
              onPaginationChange(page, pageLimit) {
                setFilterParams((prev) => ({
                  ...prev,
                  pageNumber: page,
                  pageSize: pageLimit,
                }));
              },
            }}
            onSortChanged={(params: IExtendedSortChangedEvent) =>
              setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
            }
            emptyState={{
              title: searchText
                ? t('common.noMatchesFound')
                : t('vehiclePage.emptyState.timeClockTitle'),
              description:
                searchText || selectedQuickFilterData.length > 0
                  ? ''
                  : t('vehiclePage.emptyState.timeClockDescription'),
              link:
                searchText || selectedQuickFilterData.length > 0
                  ? ''
                  : t('vehiclePage.emptyState.timeClockLink'),
              onLinkAction: () => setIsModalOpen({ isOpen: true, isEdit: false, session: null }),
            }}
          />
        </main>
      </div>
    </>
  );
};

export default VehicleTimeClockSessions;
