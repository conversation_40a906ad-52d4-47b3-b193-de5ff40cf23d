import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { AgGridReact } from 'ag-grid-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Divider, Form } from 'antd';
import Icon from '@ant-design/icons';
import {
  DeleteIcon,
  deleteSvg,
  EmailCredentialIcon,
  EyeIcon,
  HistoryIcon,
  PlusButtonIcon,
} from '@/assets';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { CellClickedEvent, CellContextMenuEvent, ICellRendererParams } from 'ag-grid-community';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { GridNames } from '@/types/AppEvents';
import CustomModal from '@/components/common/modal/CustomModal';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { useCallback } from 'react';
import ContactFormOperations from './contactsOperations';
import { IIsOpenModal, ValuesObject } from './contactsOperations/contact.types';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { customerContactsHook } from '@/api/customer/customerContact/useCustomerContact';
import { useParams } from 'react-router-dom';
import {
  ICategories,
  ICustomerContact,
  IPermissionsForContacts,
} from '@/api/customer/customerContact/customerContact.types';
import { customerCategoryHook } from '@/api/customer/useCustomerCategories';
import { defaultPagination } from '@/constant/generalConstant';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { getPaginationData, onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { useNavigationContext } from '@/hooks/useNavigationContext';

const CustomerContactsComponent = () => {
  const gridRef = useRef<AgGridReact<ICustomerContact>>(null);
  const [addContactsForm] = Form.useForm();
  const [allContacts, setAllContacts] = useState<ICustomerContact[]>();
  const [initialData, setInitialData] = useState<ICustomerContact>();
  const notificationManager = useNotificationManager();
  const [cellData, setCellData] = useState<CellContextMenuEvent<ICellRendererParams>>(
    {} as CellContextMenuEvent<ICellRendererParams>
  );
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [searchTerm, setSearchText] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<ValuesObject[]>([]);
  const [contactCategories, setCustomerCategories] = useState<ValuesObject[]>([]);
  const { data: contactCategoriesList } = customerCategoryHook.useEntity('CONTACT');

  useEffect(() => {
    if (contactCategoriesList) {
      const formattedCategoriesList = contactCategoriesList?.map((item) => {
        return {
          label: item?.name,
          value: item?.id,
          id: item?.id,
        };
      });
      setCustomerCategories(formattedCategoriesList);
    }
  }, [contactCategoriesList]);

  const { t } = useLanguage();
  const { id } = useParams<{ id: string }>();

  const {
    data: customersContactList,
    refetch,
    isLoading,
  } = customerContactsHook.useEntities(`${id}/contacts`, filterParams);
  const [isAddContactModalOpen, setIsAddContactModalOpen] = useState<IIsOpenModal>({
    isOpen: false,
    isEdit: false,
  });
  const handleContactView = useCallback(
    (data: ICellRendererParams | CellClickedEvent) => {
      const categoriesFormat = data?.data?.categories?.map((item: any) => {
        return { value: item?.id, label: item?.name };
      });
      setInitialData({ ...data.data, categories: categoriesFormat } as ICustomerContact);

      addContactsForm.setFieldsValue({
        ...data.data,
        isActive: data.data.isActive,
        address: data.data.permissions.address,
        prices: data.data.permissions.prices,
        invoice: data.data.permissions.invoices,
        categories: categoriesFormat,
      });

      setIsAddContactModalOpen({ isOpen: true, isEdit: true });
    },
    [addContactsForm]
  );

  useEffect(() => {
    if (customersContactList) {
      setAllContacts(customersContactList?.data as ICustomerContact[]);
    }
  }, [customersContactList]);

  const handleDeleteContact = customerContactsHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.columns.contactDeletedSuccessfully'),
      });
      refetch();
      customAlert.destroy();
    },
  });
  const contactDeleteConfirmation = useCallback(
    async (data: ICellRendererParams) => {
      customAlert.error({
        title: t('dashboard.customer.columns.deleteContact'),
        message: t('dashboard.customer.columns.confirmDeleteContact'),
        firstButtonFunction: async () => {
          await handleDeleteContact.mutateAsync(`${id}/contacts/${data.data.id}`);
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [handleDeleteContact, id, t]
  );
  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.contact.sortable.includes(field);
  }, []);

  const customerColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('zonePage.colDefs.name'),
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,

        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'phoneNumber',
        headerName: t('addressPage.colDefs.phone'),
        sortable: isColumnSortable('phoneNumber'),
        unSortIcon: isColumnSortable('phoneNumber'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'email',
        headerName: t('auth.email'),
        sortable: isColumnSortable('email'),
        unSortIcon: isColumnSortable('email'),
        type: 'string',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchTerm ? highlightText(params.value, searchTerm) : params.value;
        },
      },
      {
        field: 'status',
        headerName: t('dashboard.customer.columns.status'),
        sortable: isColumnSortable('status'),
        unSortIcon: isColumnSortable('status'),
        type: 'boolean',
        visible: true,
        minWidth: 200,
        flex: 1,
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.isActive ? (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-[seagreen]  flex" />
              {t('dashboard.customer.columns.active')}
            </div>
          ) : (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-error-500  flex" />
              {t('dashboard.customer.columns.inactive')}
            </div>
          );
        },
      },
      {
        field: 'invoice',
        headerName: t('sidebar.invoiceAccess'),
        sortable: isColumnSortable('invoice'),
        unSortIcon: isColumnSortable('invoice'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.invoices ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'prices',
        headerName: t('sidebar.pricesAccess'),
        sortable: isColumnSortable('price'),
        unSortIcon: isColumnSortable('price'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.prices ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'locations',
        headerName: t('sidebar.addressAccess'),
        sortable: isColumnSortable('location'),
        unSortIcon: isColumnSortable('location'),
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.permissions.address ? (
            <div>{t('common.allow')}</div>
          ) : (
            <div>{t('common.deny')}</div>
          );
        },
        visible: true,
        minWidth: 200,
        flex: 1,
      },
      {
        field: 'action',
        headerName: t('zonePage.colDefs.action'),
        pinned: 'right',
        width: 110,
        maxWidth: 200,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="flex gap-2 h-full items-center">
              <CustomTooltip
                content={
                  <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                    {t('common.added')}:{' '}
                    <span className="block w-fit text-sm font-semibold">
                      {dateFormatter(params.data?.createdAt || '')}
                    </span>
                    <hr className="border-[#0000001c]" />
                    {t('common.modified')}:{' '}
                    <span className="block w-fit text-sm font-semibold">
                      {dateFormatter(params?.data?.updatedAt) || t('common.notUpdatedYet')}
                    </span>
                    <hr className="border-[#0000001c]" />
                    {t('common.lastUpdatedBy')}:{' '}
                    <span className="block w-fit text-sm font-semibold">
                      {params?.data?.updatedByName || t('common.notUpdatedYet')}
                    </span>
                  </div>
                }
                placement="leftTop"
              >
                <div>
                  <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
                </div>
              </CustomTooltip>
              <Icon
                component={EyeIcon}
                onClick={() => handleContactView(params)}
                className="cursor-pointer"
                alt="view"
                value={'view'}
              />

              {!params.data?.isPrimary && (
                <Icon
                  onClick={() => contactDeleteConfirmation(params)}
                  component={DeleteIcon}
                  className="cursor-pointer"
                  alt="delete"
                />
              )}
            </div>
          );
        },
        visible: true,
      },
    ];
  }, [t, isColumnSortable, searchTerm, handleContactView, contactDeleteConfirmation]);

  const createContactMutation = customerContactsHook.useCreateById('contacts', id as string, {
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.contactCreatedSuccessfully'),
      });
      refetch();
    },
  });
  const updateContactMutation = customerContactsHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.contactUpdatedSuccessfully'),
      });
      refetch();
    },
  });
  const onFinish = async () => {
    const data = addContactsForm.getFieldsValue();
    const dataToSendFormat = {
      name: data.name as string,
      email: data.email as string,
      phoneCountryCode: data.phoneCountryCode as string,
      phoneNumber: data.phoneNumber as string,
      phoneExtension: data.phoneExtension as string,
      permissions: {
        prices: data.prices,
        address: data.address,
        invoices: data.invoice,
      } as IPermissionsForContacts,

      categories: selectedCategories as unknown as ICategories[],
      userId: id as string,
      isActive: (data.isActive as boolean) || false,
    };

    if (isAddContactModalOpen.isEdit) {
      await updateContactMutation.mutateAsync({
        id: `${id}/contacts/${initialData?.id}`,
        data: dataToSendFormat,
      });
    } else {
      await createContactMutation.mutateAsync(dataToSendFormat);
    }

    setIsAddContactModalOpen({ isOpen: false, isEdit: false });
    addContactsForm.resetFields();
  };

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const { isBlocked, setIsBlocked } = useNavigationContext();

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsAddContactModalOpen({ isOpen: false, isEdit: false });
          addContactsForm.resetFields();
          setIsBlocked(false);
          setInitialData({} as ICustomerContact);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsAddContactModalOpen({ isOpen: false, isEdit: false });
    setInitialData({} as ICustomerContact);
    addContactsForm.resetFields();
  }, [addContactsForm, isBlocked, setIsBlocked, t]);

  const handleResendCredentials = useCallback(() => {
    notificationManager.success({
      message: t('common.success'),
      description: t('common.alert.resendLinkSent', {
        email: addContactsForm.getFieldValue('email'),
      }),
    });
  }, [addContactsForm, notificationManager, t]);

  const paginationData = useMemo(
    () => getPaginationData(customersContactList),
    [customersContactList]
  );

  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('dashboard.customer.emailLoginDetails'),
        icon: EmailCredentialIcon as React.ElementType,
        key: 'emailLoginDetails',
        onClick: () => {
          window.print();
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
        onClick: () => contactDeleteConfirmation(cellData as unknown as ICellRendererParams),
      },
    ];
  }, [cellData, contactDeleteConfirmation, t]);

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex w-full">
        <div className="w-1/3 flex">
          {isAddContactModalOpen.isEdit && (
            <Button
              onClick={handleResendCredentials}
              className="hover:!text-black hover:!border-gray-400 border-gray-400"
            >
              {t('dashboard.customer.columns.resendCredentials')}
            </Button>
          )}
        </div>
        <div className="flex justify-end gap-3 w-2/3">
          <Button
            onClick={closeModalHandler}
            className="hover:!text-black hover:!border-gray-400 border-gray-400"
          >
            {t('common.cancel')}
          </Button>
          <Button
            disabled={initialData?.isPrimary}
            form="add-contact-form"
            htmlType="submit"
            type="primary"
            loading={isLoading}
            className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
          >
            {isAddContactModalOpen.isEdit ? t('common.update') : t('common.save')}
          </Button>
        </div>
      </footer>
    ),
    [
      closeModalHandler,
      handleResendCredentials,
      initialData?.isPrimary,
      isAddContactModalOpen.isEdit,
      isLoading,
      t,
    ]
  );
  return (
    <>
      <CustomModal
        maskClosable={false}
        modalTitle={
          isAddContactModalOpen.isEdit
            ? t('dashboard.customer.columns.updateContact')
            : t('dashboard.customer.columns.addContact')
        }
        modalDescription={t('dashboard.customer.columns.enterNewContactDetails')}
        open={isAddContactModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        destroyOnClose
        keyboard={false}
      >
        <CustomDivider label={t('zonePage.basicDetails')} />
        <ContactFormOperations
          form={addContactsForm}
          onFinish={onFinish}
          currentData={initialData as ICustomerContact}
          isAddContactModalOpen={isAddContactModalOpen}
          setSelectedCategories={setSelectedCategories}
          contactCategories={contactCategories}
        />
      </CustomModal>
      <div className="flex h-full">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <>
              <div className="flex gap-3">
                <SearchFilterComponent
                  onSearch={searchHandler}
                  colDefs={customerColDefs}
                  advanceFilter={false} // TODO: As of now, we don't need advance filter hear
                  searchInputPlaceholder={t('dashboard.customer.columns.searchContact')}
                />
                <ColumnManage colDefs={customerColDefs} gridName={GridNames.contactGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
              </div>
            </>

            <div className="pt-5">
              <Button
                className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
                loading={isLoading}
                onClick={() => setIsAddContactModalOpen({ isOpen: true, isEdit: false })}
              >
                {t('dashboard.customer.columns.addContact')}
              </Button>
            </div>
          </div>
          <main className="h-full overflow-x-hidden  bg-white">
            <div className="mx-auto pr-6 py-5 ">
              <CustomAgGrid
                gridRef={gridRef}
                gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
                loading={isLoading}
                rowData={allContacts}
                columnDefs={customerColDefs}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                className="3xsm:!h-[50vh] md:!h-[72vh] lg:!h-[70vh] 3xl:!h-[70vh]"
                gridName={GridNames.contactGrid}
                isContextMenu
                onCellClicked={(params) => {
                  if (params.colDef.field !== 'action') {
                    handleContactView(params);
                  }
                }}
                contextMenuItem={customerContextMenuItems}
                onContextMenu={(params: CellContextMenuEvent<ICellRendererParams>) =>
                  setCellData(params)
                }
                emptyState={{
                  title: searchTerm
                    ? t('common.noMatchesFound')
                    : t('dashboard.customer.contactsEmptyState.title'),
                  description: searchTerm
                    ? ''
                    : t('dashboard.customer.contactsEmptyState.description'),
                  link: searchTerm ? '' : t('dashboard.customer.contactsEmptyState.link'),
                  onLinkAction: () => setIsAddContactModalOpen({ isOpen: true, isEdit: false }),
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default CustomerContactsComponent;
