import { GetAddressDto } from '@/api/address/address.types';
import { IIsOpenModal } from '@/types/CommonTypes';
import { FormInstance } from 'antd';

export interface ICustomerAddress {
  id?: string;
  companyName: string;
  name: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  province: string;
  country: string;
  postalCode: string;
  phone: string;
  phoneExtension: string;
  email: string;
  notes: string;
  createdAt: string;
  updated: string;
  lastUpdateBy: string;
  phoneNumberCountryCode: string;
  zone: string;
}
export interface ICustomerAddressOperationFormProps {
  form: FormInstance<GetAddressDto>;
  onFinish: (formValues: GetAddressDto) => Promise<void>;
  open: IIsOpenModal;
}
