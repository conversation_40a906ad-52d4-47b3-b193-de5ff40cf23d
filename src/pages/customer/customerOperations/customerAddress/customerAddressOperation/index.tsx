import CustomDivider from '@/components/common/divider/CustomDivider';
import { useLanguage } from '@/hooks/useLanguage';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Form, Input, InputNumber, InputRef, Select, Space } from 'antd';
import MaskedInput from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import TextArea from 'antd/es/input/TextArea';
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { formErrorRegex } from '@/constant/Regex';
import { ICustomerAddressOperationFormProps } from '../customerAddress.types';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { infoCircleOutlined } from '@/assets';
import { Autocomplete } from '@react-google-maps/api';
import { useGooglePlaceDropdownFix } from '@/hooks/useGooglePlaceDropdownFix';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { AxiosError } from 'axios';
import { zoneService } from '@/api/zones/useZones';
import { TrackedError } from '@/types/AxiosTypes';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';
import { isFormChangedHandler } from '@/lib/helper';
import { useNavigationContext } from '@/hooks/useNavigationContext';

const CustomerAddressOperationForm: React.FC<ICustomerAddressOperationFormProps> = (props) => {
  const { form, onFinish, open } = props;

  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const inputPhoneRef = useRef<InputRef>(null);
  const InitialValue = useMemo(() => (open.isEdit ? form.getFieldsValue(true) : {}), [open.isEdit]);
  const postalCodeWatcherValue = Form.useWatch('postalCode', form);

  const { t } = useLanguage();

  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const autocompleteRef = useRef<Autocomplete>(null);

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);

  const PhoneNumberAddonBefore = (
    <Form.Item name="countryCode" noStyle initialValue={'USA +1'}>
      <Select
        options={optionsForPrefix}
        placeholder={t('common.usa')}
        onChange={(value) => maskingInputPhone(value)}
      ></Select>
    </Form.Item>
  );

  useGooglePlaceDropdownFix(open.isOpen, 'autoCompleteForCustomerAddress');

  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const { setIsBlocked } = useNavigationContext();

  const onPlaceChanged = useCallback(async () => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);
      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
        latitude: selectedPlaceData?.latitude,
        longitude: selectedPlaceData?.longitude,
      };
      form.setFieldsValue(newFormValues);
      try {
        form.resetFields(['zone']);
        if (selectedPlaceData?.postalCode) {
          const trimmedPostalCode = selectedPlaceData?.postalCode.split(' ')[0];
          const response = await zoneService.getById(`postalCode/${trimmedPostalCode}`);
          form.setFieldValue('zone', response.name);
          await form.validateFields(['zone']);
        } else {
          form.setFields([
            {
              name: 'zone',
              errors: [t('addressPage.operationalForm.noPostalCodeFound')],
            },
          ]);
        }
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorStack = error?.response?.data as TrackedError;
          if (errorStack?.code === 406007) {
            form.resetFields(['zone']);
            form.setFields([
              {
                name: 'zone',
                errors: [
                  t('addressPage.operationalForm.zoneError', {
                    code: errorStack?.details?.postalCode.split(' ')[0],
                  }),
                ],
              },
            ]);
          }
        }
      }
    }
  }, [form, searchResult, t]);

  useEffect(() => {
    maskingInputPhone(form.getFieldValue('countryCode'), false);
  }, [form, maskPhoneInput, maskingInputPhone]);

  const onAutocompleteChangeHandler = () => {
    const val = form.getFieldsValue(['province', 'city', 'country', 'postalCode']);
    if (val.province || val.city || val.country || val.postalCode) {
      form.resetFields(['province', 'city', 'country', 'postalCode', 'zone']);
    }
  };

  return (
    <Form
      name="customer-address-form"
      layout="vertical"
      className="custom-form"
      form={form}
      scrollToFirstError={{ behavior: 'smooth' }}
      onFinish={onFinish}
      onFieldsChange={(changesFields) => {
        //TODO: refactor logic for line 146 line
        if (
          changesFields.length <= 1 &&
          !changesFields.some((field) => field.name.includes('zone'))
        ) {
          const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true), [
            'zone',
            'countryCode',
          ]);
          setIsBlocked(isIsChange);
        } else if (changesFields.length > 1) {
          setIsBlocked(false);
        }
      }}
      preserve={false}
    >
      <CustomDivider label={t('common.divider.basicDetails')} />
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.name')}
            validateFirst
            name="name"
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.nameError') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              {
                pattern: formErrorRegex.NoSpecialCharacters,
                message: t('common.errors.noSpacialCharacters'),
              },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.namePlaceholder')}
              maxLength={50}
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.companyName')}
            name="companyName"
            validateFirst
            rules={[
              {
                required: true,
                message: t('customerAddressPage.operationalForm.companyNameError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.companyNamePlaceholder')}
              maxLength={50}
            />
          </Form.Item>
        </div>

        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.email')}
            name="email"
            validateFirst
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.emailError') },
              { type: 'email', message: t('customerAddressPage.operationalForm.emailTypeError') },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  const [localPart] = value.split('@');
                  if (localPart && localPart.length > 64) {
                    return Promise.reject(
                      new Error('The part before @ cannot exceed 64 characters')
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.emailPlaceholder')}
              maxLength={255}
            />
          </Form.Item>
          <Space.Compact className="combined-input">
            <Form.Item
              className="w-[75%]"
              dependencies={['countryCode']}
              validateFirst
              rules={[
                {
                  required: true,
                  validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                },
                {
                  validator: (_, value) =>
                    validateMaskedInput(
                      value,
                      maskPhoneInput.length,
                      t('customerAddressPage.operationalForm.validPhoneNumberError')
                    ),
                },
              ]}
              name="phoneNumber"
              label={t('customerAddressPage.operationalForm.phoneNumber')}
            >
              <MaskedInput
                ref={inputPhoneRef}
                addonBefore={PhoneNumberAddonBefore}
                className="customer-general-maskedInput address-popup-maskedInput"
                placeholder={t('customerAddressPage.operationalForm.phoneNumberPlaceholder')}
                mask={maskPhoneInput.mask}
                onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
              />
            </Form.Item>
            <Form.Item name="phoneExtension" className="w-[25%]">
              <Input
                placeholder="00000"
                maxLength={6}
                onKeyDown={(event) => numberFieldValidator(event, {})}
              />
            </Form.Item>
          </Space.Compact>
        </div>
        <CustomDivider
          label={t('customerAddressPage.operationalForm.locationDividerText')}
          className="py-2"
        />
        <CustomGoogleAutoComplete
          onLoad={onLoad}
          onPlaceChanged={onPlaceChanged}
          ref={autocompleteRef}
        >
          <Form.Item
            label={t('customerAddressPage.operationalForm.addressLine1')}
            rules={[
              {
                required: true,
                message: t('customerAddressPage.operationalForm.addressLine1Error'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
            name="addressLine1"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.addressLine1Placeholder')}
              maxLength={255}
              id="autoCompleteForCustomerAddress"
              onChange={onAutocompleteChangeHandler}
            />
          </Form.Item>
        </CustomGoogleAutoComplete>
        <Form.Item name="latitude" hidden preserve>
          <InputNumber />
        </Form.Item>
        <Form.Item name="longitude" hidden preserve>
          <InputNumber />
        </Form.Item>
        <Form.Item
          label={t('customerAddressPage.operationalForm.addressLine2')}
          name="addressLine2"
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
        >
          <Input
            placeholder={t('customerAddressPage.operationalForm.addressLine2Placeholder')}
            maxLength={255}
          />
        </Form.Item>
        <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
          <Form.Item
            label={t('customerAddressPage.operationalForm.city')}
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.cityError') },
            ]}
            name="city"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.cityPlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>

          <Form.Item
            label={t('customerAddressPage.operationalForm.province')}
            rules={
              [
                // { required: true, message: t('customerAddressPage.operationalForm.provinceError') },
              ]
            }
            name="province"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.provincePlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.postalCode')}
            validateFirst
            rules={[
              { required: true, message: t('customerAddressPage.operationalForm.postalCodeError') },
              {
                pattern: formErrorRegex.PostalCode,
                message: t('customerAddressPage.operationalForm.validPostalCodeError'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
            name="postalCode"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.postalCodePlaceholder')}
              maxLength={20}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={t('customerAddressPage.operationalForm.country')}
            rules={
              [
                // { required: true, message: t('customerAddressPage.operationalForm.countryError') },
              ]
            }
            name="country"
          >
            <Input
              placeholder={t('customerAddressPage.operationalForm.countryPlaceholder')}
              maxLength={100}
              disabled
            />
          </Form.Item>
        </div>
        <Form.Item
          validateFirst
          label={
            <span className="flex gap-1">
              {t('customerAddressPage.operationalForm.zone')}
              <CustomTooltip title={t('customerAddressPage.operationalForm.zoneToolTip')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
          rules={[
            {
              required: true,
              message: postalCodeWatcherValue
                ? t('addressPage.operationalForm.zoneError', {
                    code: postalCodeWatcherValue.split(' ')[0],
                  })
                : t('addressPage.operationalForm.noPostalCodeFound'),
            },
          ]}
          name="zone"
          dependencies={['addressLine1']}
        >
          <Input maxLength={50} disabled />
        </Form.Item>

        <Form.Item
          label={
            <span className="flex gap-1">
              {t('customerAddressPage.operationalForm.comments')}
              <CustomTooltip title={t('customerAddressPage.operationalForm.notesTooltip')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
          name="notes"
        >
          <TextArea
            placeholder={t('customerAddressPage.operationalForm.commentsPlaceHolder')}
            maxLength={500}
            style={{
              minHeight: 54,
              maxHeight: 100,
            }}
          />
        </Form.Item>
      </div>
    </Form>
  );
};

export default memo(CustomerAddressOperationForm);
