import { FormInstance } from 'antd';

export interface INotificationSettings {
  [key: string]: {
    [key: string]: {
      [key: string]: boolean;
    };
  };
}
export interface INotificationSettingsData {
  [key: string]: {
    [key: string]: {
      [key: string]: {
        [key: string]: boolean;
      };
    };
  };
}
export interface ISettingsUiConfigData {
  fieldIdentifier: string;
  label: string;
  isRequired: boolean;
  isSystemRequired: boolean;
  isVisible: boolean;
  placeholder?: string;
  tooltip: string;
  hasChildren: boolean;
  children: ISettingsUiConfigData[];
}
export interface ISwitchChanged {
  [key: string]: boolean;
}
export interface ToggleState {
  [key: string]: boolean;
}
export interface INotificationTabContentProps {
  notificationStatusData: INotificationSettings;
  handleSaveNotificationSettings: (data: INotificationSettings, key: string) => void;
  currentSelectedKey: string;
  onSwitchChangeHandler: (settings: INotificationSettings) => void;
}
export interface ISettingTabs {
  key: string;
  label: string;
  icon?: string;
}
export interface ISettingsUIConfigurationProps {
  UiConfigurationData: ISettingsUiConfigData[];
}
export interface ISettingsUiTabContentProps {
  UiConfigData: ISettingsUiConfigData;
}
export interface ISettingsPackageType {
  id?: string;
  visible?: boolean;
  packagingType: string;
  status: string;
  weight: number;
  height: number;
  width: number;
  length: number;
}
export interface IPackageTypeFormProps {
  form: FormInstance<ISettingsPackageType>;
  onFinish: (values: ISettingsPackageType) => void;
}
