import { useMemo, useState } from 'react';
import CustomerServiceGrid from './CustomerServiceGrid';
import { ICustomerService, IServiceSearchHighlight } from './customerServiceTypes';
import { priceSetHook } from '@/api/priceSet/usePriceSet';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import { useLanguage } from '@/hooks/useLanguage';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { useParams } from 'react-router-dom';
import { IPriceSet } from '@/api/priceSet/priceSet.types';
import {
  assignedPriceSetsService,
  assignedPriceSetsHook,
} from '@/api/customer/assignedPriceSets/useAssignedPriceSets';
import { IGetAssignedPriceSets } from '@/api/customer/assignedPriceSets/assignedPriceSet.types';
import notificationManagerInstance from '@/hooks/useNotificationManger';

const CustomerService = () => {
  const [isEdit, setIsEdit] = useState(false);

  const [searchText, setSearchText] = useState<IServiceSearchHighlight>({
    searchTextForAvailable: '',
    searchTextForSelected: '',
    searchTextForAssigned: '',
  });
  const { id: customerId } = useParams();
  const { data: assignedServices, refetch: refetchAssignedServices } =
    assignedPriceSetsHook.useEntities(`${customerId}/priceSets`);

  const { data: customerServices } = priceSetHook.useEntities('all/minimal');

  const { t } = useLanguage();

  const customerServiceColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'internalName',
        headerName: t('dashboard.customer.services.colDefs.serviceLevel'),
        unSortIcon: true,
        tooltipField: 'serviceName',
        type: 'string',
        visible: true,
        flex: 1,
        minWidth: 250,
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          if (params.context) {
            const isAvailableGrid = params.context.name === 'available';
            const relevantSearchText = isAvailableGrid
              ? searchText.searchTextForAvailable
              : searchText.searchTextForSelected;

            return (
              (relevantSearchText && highlightText(params.value, relevantSearchText)) ||
              params.value
            );
          }
          return searchText.searchTextForAssigned
            ? highlightText(params.value, searchText.searchTextForAssigned)
            : params.value;
        },
      },
      {
        field: 'name',
        headerName: t('dashboard.customer.services.colDefs.serviceName'),
        unSortIcon: true,
        visible: true,
        flex: 1,
        minWidth: 250,
        type: 'string',
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          if (params.context) {
            const isAvailableGrid = params.context.name === 'available';
            const relevantSearchText = isAvailableGrid
              ? searchText.searchTextForAvailable
              : searchText.searchTextForSelected;

            return (
              (relevantSearchText && highlightText(params.value, relevantSearchText)) ||
              params.value
            );
          }
          return searchText.searchTextForAssigned
            ? highlightText(params.value, searchText.searchTextForAssigned)
            : params.value;
        },
      },
    ];
  }, [
    searchText.searchTextForAssigned,
    searchText.searchTextForAvailable,
    searchText.searchTextForSelected,
    t,
  ]);

  const onSave = async (selectedData: ICustomerService[]) => {
    const priceSetIds = selectedData.map((data) => data.id);
    try {
      await assignedPriceSetsService.update(`${customerId}/priceSets`, { priceSetIds });
      await refetchAssignedServices();
      notificationManagerInstance.success({
        message: t('common.success'),
        description: 'Services assigned to this customer successfully',
      });
    } catch (error) {
      notificationManagerInstance.error({
        message: t('common.error'),
        description: t('systemErrors.whileAssigningServices'),
      });
    }
  };

  const unAssignedServices = useMemo(() => {
    return customerServices?.data.filter(
      (service) =>
        !assignedServices?.data.some((assignedService) => {
          return assignedService.priceSetId === service.id;
        })
    );
  }, [assignedServices, customerServices]);

  const assignedServicesMasked = useMemo(() => {
    return assignedServices?.data.map((service) => ({ ...service, id: service.priceSetId }));
  }, [assignedServices]) as IGetAssignedPriceSets[];

  return (
    <>
      {isEdit ? (
        <TransferGrid<IPriceSet>
          setIsEdit={setIsEdit}
          colDefs={customerServiceColDefs}
          initialRowData={unAssignedServices || []}
          assignedServices={assignedServicesMasked || []}
          setSearchText={setSearchText}
          searchText={searchText}
          onSave={onSave}
          mainHeaderTitle={t('dashboard.customer.services.assignServices')}
          availableGridHeader={t('dashboard.customer.services.unassignedServices')}
          availableGridSearchPlaceholder={t('dashboard.customer.services.searchServiceName')}
          selectedGridHeader={t('dashboard.customer.services.assignedServices')}
          selectedGridSearchPlaceholder={t('dashboard.customer.services.searchServiceName')}
          availableGridEmptyStateTitle={t('dashboard.customer.services.noServices')}
          availableGridEmptyStateDescription={t(
            'dashboard.customer.services.noServicesAvailableToAssign'
          )}
          selectedGridEmptyState={t('dashboard.customer.services.noServicesAssigned')}
          navigationAndRefreshBlocker
          isDataRequired={false}
        />
      ) : (
        <CustomerServiceGrid
          setIsEdit={setIsEdit}
          allServices={assignedServicesMasked || []}
          noServiceAvailableInSystem={customerServices?.data.length === 0}
          colDefs={customerServiceColDefs}
          searchText={searchText}
          setSearchText={setSearchText}
        />
      )}
    </>
  );
};
export default CustomerService;
