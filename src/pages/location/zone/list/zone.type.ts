import { CreateZoneDto } from '@/api/zones/zone.types';
import { FormInstance } from 'antd';

export interface IZone {
  id?: string;
  name: string;
  postalCodes: string[];
  comment?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  updatedByName?: string;
}

export interface ZoneProps {
  searchText?: string;
  onEditZone?: (zone: IZone) => void;
  onDeleteZone?: (zone: IZone) => void;
  alreadyInUsedPostalCodes?: string[];
  isColumnSortable: (field: string) => boolean;
}

export interface ZoneTableProps {
  searchText?: string;
  onEditZoneTable?: (zone: IZoneLookupTable) => void;
  onDeleteZoneTable?: (zone: IZoneLookupTable) => void;
}

export interface IIsOpenModal {
  isOpen: boolean;
  isEdit?: boolean;
}

export interface IZoneOperationFormProps {
  form: FormInstance<CreateZoneDto>;
  onFinish: (values: CreateZoneDto) => Promise<void>;
  open: IIsOpenModal;
  refreshGrid: () => void;
  initialData?: IZone;
  setIsModalOpen: (isOpen: IIsOpenModal) => void;
  alreadyInUsedPostalCodes?: string[];
  setIsUsedPostalCode: (codes: string[]) => void;
  isUsedPostalCode: string[];
}

export interface IZoneLookupRow {
  fromId: string;
  toId: string;
  value: number | string | null;
}

export interface IZoneLookupTable {
  id?: string;
  name: string;
  data: IZoneLookupRow[];
  isReversePricing: boolean;
}

export interface IAdjustCellsType {
  type: 'fixed' | 'percentage';
}
