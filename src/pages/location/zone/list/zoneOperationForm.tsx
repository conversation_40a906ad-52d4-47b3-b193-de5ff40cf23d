import { IZoneOperationFormProps } from './zone.type';
import { Form, Input, Select, SelectProps, Tag } from 'antd';
import React, { memo, useMemo } from 'react';
import { useLanguage } from '@hooks/useLanguage.ts';
import TextArea from 'antd/es/input/TextArea';
import './ZoneOperation.css';
import { formErrorRegex } from '@/constant/Regex';
import { isFormChangedHandler } from '@/lib/helper';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import usePreventExits from '@/hooks/usePreventExits';

const ZoneOperationForm: React.FC<IZoneOperationFormProps> = (props) => {
  const {
    form,
    onFinish,
    open,
    initialData,
    alreadyInUsedPostalCodes,
    setIsUsedPostalCode,
    isUsedPostalCode,
  } = props;
  const { t } = useLanguage();
  type TagRender = SelectProps['tagRender'];

  const tagRender: TagRender = (props) => {
    const { label, closable, onClose } = props;

    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };

    const isUsed = alreadyInUsedPostalCodes?.includes(label?.toString().toUpperCase() ?? '');

    return (
      <Tag
        className={`h-[30px] flex items-center text-[14px] mt-[3px] ${isUsed ? 'bg-primary-25 text-primary-600 border border-primary-100' : 'bg-[#FCFCFD] text-[#20363F] border border-[#CDD7DB]'}`}
        onMouseDown={onPreventMouseDown}
        closable={closable}
        onClose={onClose}
        style={{ marginInlineEnd: 4 }}
      >
        {label}
      </Tag>
    );
  };

  const handlePostalCodesChange = (value: string[]) => {
    let upperValue = value.map((v: string) => v.toUpperCase());
    upperValue = [...new Set(upperValue)];
    const currentlySelectedCode = upperValue.filter(
      (postalCode: string) => !initialData?.postalCodes?.includes(postalCode)
    );
    const usedCodes = currentlySelectedCode.filter((code) =>
      alreadyInUsedPostalCodes?.includes(code.toUpperCase())
    );
    setIsUsedPostalCode(usedCodes);
    form.setFieldsValue({ postalCodes: upperValue });
    
    setTimeout(() => {
      form.validateFields(['postalCodes']);
    }, 0);
  };

  const InitialValue = useMemo(
    () =>
      open.isEdit
        ? { ...form.getFieldsValue(true), notes: form.getFieldsValue(true).notes || '' }
        : {},
    [open.isEdit]
  );

  const { setPreventExit } = usePreventExits();
  const { setIsBlocked } = useNavigationContext();
  return (
    <Form
      name="zone-form"
      layout="vertical"
      className="custom-form"
      form={form}
      onFinish={onFinish}
      onFieldsChange={(changesFields) => {
        if (changesFields.length <= 1) {
          const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true), [
            'zone',
            'countryCode',
          ]);
          setPreventExit(isIsChange);
        } else if (changesFields.length > 1) {
          setIsBlocked(false);
          setPreventExit(false);
        }
      }}
      preserve={false}
    >
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <Form.Item
          validateFirst
          className="w-full"
          label={t('zonePage.zoneNameField')}
          name="name"
          rules={[
            {
              required: true,
              message: t('zonePage.operationalForm.nameError') || 'Name is required',
            },
            {
              pattern: formErrorRegex.NAME_PATTERN,
              message: `${t('zonePage.nameValidation')}`,
            },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              validator(_, value: string) {
                if (value.length > 50) {
                  return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input placeholder={t('zonePage.operationalForm.namePlaceholder') || 'Enter zone name'} />
        </Form.Item>

        <Form.Item
          validateFirst
          className="w-full"
          label={t('zonePage.colDefs.postalCodes') || 'Postal Codes'}
          name="postalCodes"
          rules={[
            {
              required: true,
              message: t('zonePage.operationalForm.postalCodesError'),
            },
            () => ({
              validator(_, value: string[]) {
                if (!value || value.length === 0) {
                  return Promise.reject(new Error(t('zonePage.atLeastOnePostalCode')));
                }
                const invalidCodes = value.filter((code) => !formErrorRegex.FSA_REGEX.test(code));
                if (invalidCodes.length > 0) {
                  return Promise.reject(
                    new Error(
                      `Invalid postal code(s): ${invalidCodes.join(', ')}. Must match A1A pattern.`
                    )
                  );
                }
                return Promise.resolve();
              },
            }),
          ]}
        >
          <Select
            className="zone-select"
            mode="tags"
            placeholder={
              t('zonePage.operationalForm.postalCodesPlaceholder') ||
              'Enter postal codes (A1A format)'
            }
            tokenSeparators={[',']}
            onChange={handlePostalCodesChange}
            value={form.getFieldValue('postalCodes')}
            tagRender={tagRender}
          />
          {isUsedPostalCode.length > 0 && (
            <span className="text-primary-600 text-sm p-1">
              {isUsedPostalCode.length > 1
                ? t('zonePage.codesAreAddedInAnotherZone', { code: isUsedPostalCode.join(', ') })
                : t('zonePage.codeIsAddedInAnotherZone', { code: isUsedPostalCode.join(', ') })}
            </span>
          )}
        </Form.Item>

        <Form.Item
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
          label={t('zonePage.operationalForm.comments')}
          name="notes"
        >
          <TextArea
            placeholder={
              t('zonePage.operationalForm.commentsPlaceholder') ||
              'Additional details about this zone'
            }
            maxLength={2500}
            style={{
              minHeight: 54,
              maxHeight: 100,
            }}
          />
        </Form.Item>
      </div>
    </Form>
  );
};

export default memo(ZoneOperationForm);
