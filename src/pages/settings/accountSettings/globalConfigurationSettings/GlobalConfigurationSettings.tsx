import { <PERSON><PERSON>, Button, Form, Radio, Select, Typography } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

const GlobalConfigurationSettings = () => {
  const { t } = useLanguage();

  const timeZoneOptions = [
    {
      label: t('settings.globalConfiguration.timeZoneOptions.asiaKolkata'),
      value: 'Asia/Kolkata',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaNewYork'),
      value: 'America/New_York',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaChicago'),
      value: 'America/Chicago',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaDenver'),
      value: 'America/Denver',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaLosAngeles'),
      value: 'America/Los_Angeles',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaToronto'),
      value: 'America/Toronto',
    },
    {
      label: t('settings.globalConfiguration.timeZoneOptions.americaVancouver'),
      value: 'America/Vancouver',
    },
  ];

  const languageOptions = [
    {
      label: t('settings.globalConfiguration.languageOptions.english'),
      value: 'en',
    },
    {
      label: t('settings.globalConfiguration.languageOptions.french'),
      value: 'fr',
    },
  ];

  return (
    <div className="pt-4 pr-4">
      <Alert
        message={<span className="font-semibold">{t('settings.globalConfiguration.title')}</span>}
        description={
          <p className="text-sm text-[#090A1A]">{t('settings.globalConfiguration.description')}</p>
        }
        type="info"
        className="mb-4"
        closable
        onClose={() => {}}
      />
      <Form name="company-details-form" layout="vertical" className="custom-form">
        <div className="flex gap-4 flex-col">
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Form.Item
              label={t('settings.globalConfiguration.timeZone')}
              name="timezone"
              rules={[
                {
                  required: true,
                  message: t('settings.globalConfiguration.timeZoneRequired'),
                },
              ]}
            >
              <Select
                placeholder={t('settings.globalConfiguration.timeZonePlaceholder')}
                options={timeZoneOptions}
                prefixCls="custom-select"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              label={t('settings.globalConfiguration.language')}
              name="language"
              rules={[
                {
                  required: true,
                  message: t('settings.globalConfiguration.languageRequired'),
                },
              ]}
            >
              <Select
                placeholder={t('settings.globalConfiguration.languagePlaceholder')}
                options={languageOptions}
                prefixCls="custom-select"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </div>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  {t('settings.globalConfiguration.timeFormat')}
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  {t('settings.globalConfiguration.timeFormatDescription')}
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="timeFormat" initialValue={'12hr'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="12hr">
                      {t('settings.globalConfiguration.timeFormatOptions.twelveHour')}
                    </Radio.Button>
                    <Radio.Button value="24hr">
                      {t('settings.globalConfiguration.timeFormatOptions.twentyFourHour')}
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  {t('settings.globalConfiguration.distanceMeasure')}
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  {t('settings.globalConfiguration.distanceMeasureDescription')}
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="distanceMeasure" initialValue={'km'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="km">
                      {t('settings.globalConfiguration.distanceOptions.kilometers')}
                    </Radio.Button>
                    <Radio.Button value="miles">
                      {t('settings.globalConfiguration.distanceOptions.miles')}
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  {t('settings.globalConfiguration.weightMeasure')}
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  {t('settings.globalConfiguration.weightMeasureDescription')}
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="weightMeasure" initialValue={'lb'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="lb">
                      {t('settings.globalConfiguration.weightOptions.pounds')}
                    </Radio.Button>
                    <Radio.Button value="kg">
                      {t('settings.globalConfiguration.weightOptions.kilograms')}
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  {t('settings.globalConfiguration.lengthMeasure')}
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  {t('settings.globalConfiguration.lengthMeasureDescription')}
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="lengthMeasure" initialValue={'cm'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="cm">
                      {t('settings.globalConfiguration.lengthOptions.centimeters')}
                    </Radio.Button>
                    <Radio.Button value="inch">
                      {t('settings.globalConfiguration.lengthOptions.inches')}
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-start mt-4">
          <Button type="primary" htmlType="submit" className="h-[40px] px-8">
            {t('settings.globalConfiguration.save')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default GlobalConfigurationSettings;
