import { <PERSON><PERSON>, Button, Form, Radio, Select, Typography } from 'antd';

const GlobalConfigurationSettings = () => {
  const timeZoneOptions = [
    {
      label: 'Asia/Kolkata (GMT+05:30)',
      value: 'Asia/Kolkata',
    },
    {
      label: 'America/New_York (GMT-05:00)',
      value: 'America/New_York',
    },
    {
      label: 'America/Chicago (GMT-06:00)',
      value: 'America/Chicago',
    },
    {
      label: 'America/Denver (GMT-07:00)',
      value: 'America/Denver',
    },
    {
      label: 'America/Los_Angeles (GMT-08:00)',
      value: 'America/Los_Angeles',
    },
    {
      label: 'America/Toronto (GMT-05:00)',
      value: 'America/Toronto',
    },
    {
      label: 'America/Vancouver (GMT-08:00)',
      value: 'America/Vancouver',
    },
  ];

  const languageOptions = [
    {
      label: 'English',
      value: 'en',
    },
    {
      label: 'French',
      value: 'fr',
    },
  ];

  return (
    <div className="pt-4 pr-4">
      <Alert
        message={<span className="font-semibold">Global configuration</span>}
        description={
          <p className="text-sm text-[#090A1A]">
            Set platform-wide preferences, including language, time zone, and measurement units, to
            ensure a seamless experience for all users.
          </p>
        }
        type="info"
        className="mb-4"
        closable
        onClose={() => {}}
      />
      <Form name="company-details-form" layout="vertical" className="custom-form">
        <div className="flex gap-4 flex-col">
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Form.Item
              label="Time zone"
              name="timezone"
              rules={[{ required: true, message: 'Time zone is required' }]}
            >
              <Select
                placeholder="Select time zone"
                options={timeZoneOptions}
                prefixCls="custom-select"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              label="Language"
              name="language"
              rules={[{ required: true, message: 'Language is required' }]}
            >
              <Select
                placeholder="Select language"
                options={languageOptions}
                prefixCls="custom-select"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </div>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  Time format
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  Choose between 12-hour and 24-hour time displays to match your preference or
                  region effortlessly.
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="timeFormat" initialValue={'12hr'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="12hr">12hr</Radio.Button>
                    <Radio.Button value="24hr">24hr</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  Distance measure
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  Toggle between miles and kilometers for a more intuitive understanding of
                  distances
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="distanceMeasure" initialValue={'km'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="km">Km</Radio.Button>
                    <Radio.Button value="miles">Miles</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  Weight measure
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  Select pounds or kilograms to ensure accuracy and comfort in weight tracking.
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="weightMeasure" initialValue={'lb'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="lb">Lb</Radio.Button>
                    <Radio.Button value="kg">Kg</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
            <div className="w-full flex gap-2 justify-between doNotWrap">
              <div className="flex flex-col gap-1 mb-2 w-[75%]">
                <Typography.Text className="font-semibold !text-[#20363f]">
                  Length measure
                </Typography.Text>
                <Typography.Text className="text-xs text-primary-200 ">
                  Switch between inches, centimeters and feet for clear and precise measurements in
                  every task.
                </Typography.Text>
              </div>
              <div>
                <Form.Item name="lengthMeasure" initialValue={'cm'} noStyle>
                  <Radio.Group buttonStyle="solid">
                    <Radio.Button value="cm">Cm</Radio.Button>
                    <Radio.Button value="inch">Inch</Radio.Button>
                  </Radio.Group>
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-start mt-4">
          <Button type="primary" htmlType="submit" className="h-[40px] px-8">
            Save
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default GlobalConfigurationSettings;
