import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import CompanyDetailsSettings from './companyDetailsSettings/CompanyDetailsSettings';
import GlobalConfigurationSettings from './globalConfigurationSettings/GlobalConfigurationSettings';
import { TabsComponent } from '@/components/common/customTabs/CustomTabs';
import { useParams } from 'react-router-dom';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';

const AccountSettings = () => {
  const { tab: currentTab } = useParams<{ tab: string }>();
  const { navigate } = useNavigationContext();
  const defaultBreadCrumbItems = [
    {
      key: 'companyDetails',
      label: 'Company Details',
      children: <CompanyDetailsSettings />,
      tabKey: 'Company Details',
    },
    {
      key: 'globalConfiguration',
      label: 'Global Configuration',
      children: <GlobalConfigurationSettings />,
      tabKey: 'Global Configuration',
    },
  ];

  return (
    <div className="h-full">
      <div className="flex justify-start items-center mb-4">
        <PageHeadingComponent title={'Account Settings'} />
      </div>
      <main>
        <TabsComponent
          tabs={defaultBreadCrumbItems}
          activeKey={currentTab}
          defaultActiveKey="companyDetails"
          // destroyInactiveTabPane
          onChange={(key) => {
            navigate(ROUTES.SETTINGS.SETTINGS_ACCOUNT.replace(':tab', key));
          }}
        />
      </main>
    </div>
  );
};

export default AccountSettings;
