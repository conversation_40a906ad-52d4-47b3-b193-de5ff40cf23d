import FacebookPrefixIcon from '@/assets/icons/fecebookPrefixIcon';
import InstagramPrefixIcon from '@/assets/icons/instagramPrefixIcon';
import LinkedinPrefixIcon from '@/assets/icons/linkedinPrefixIcon';
import WhatsappPrefixIcon from '@/assets/icons/whatsappPrefixIcon';
import XPrefixIcon from '@/assets/icons/xPrefixIcon';
import YoutubePrefixIcon from '@/assets/icons/youtubePrefixIcon';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import { formErrorRegex } from '@/constant/Regex';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { useLanguage } from '@/hooks/useLanguage';
import { Alert, Button, Form, Input, InputRef, Select, Space, Typography } from 'antd';
import MaskedInput, { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { useCallback, useRef, useState } from 'react';

const CompanyDetailsSettings = () => {
  const { t } = useLanguage();
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [maskFaxInput, setMaskFaxInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const inputPhoneRef = useRef<InputRef>(null);
  const inputFaxRef = useRef<InputRef>(null);
  const [form] = Form.useForm();

  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    if (focus) {
      inputPhoneRef?.current?.focus();
    }
  }, []);

  const maskingInputFaxNumber = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskFaxInput(selectedCountryMask);
    if (focus) {
      inputFaxRef?.current?.focus();
    }
  }, []);

  const PhoneNumberAddonBefore = (
    <Form.Item name="countryCode" noStyle initialValue={'USA +1'}>
      <Select
        options={optionsForPrefix}
        className="!max-w-[100px]"
        onChange={(value) => maskingInputPhone(value)}
      ></Select>
    </Form.Item>
  );

  const faxNumberAddonBefore = (
    <Form.Item name="faxCountryCode" noStyle initialValue={'USA +1'}>
      <Select
        options={optionsForPrefix}
        className="!max-w-[100px]"
        onChange={(value) => maskingInputFaxNumber(value)}
      ></Select>
    </Form.Item>
  );

  const FacebookFieldPrefix = (
    <div className="flex gap-2 items-center">
      <FacebookPrefixIcon />
      <span className="text-primary-200">facebook.com/</span>
    </div>
  );
  const XFieldPrefix = (
    <div className="flex gap-2 items-center">
      <XPrefixIcon />
      <span className="text-primary-200">x.com/</span>
    </div>
  );

  const LinkedInFieldPrefix = (
    <div className="flex gap-2 items-center">
      <LinkedinPrefixIcon />
      <span className="text-primary-200">linkedin.com/</span>
    </div>
  );

  const InstagramFieldPrefix = (
    <div className="flex gap-2 items-center">
      <InstagramPrefixIcon />
      <span className="text-primary-200">instagram.com/</span>
    </div>
  );

  const WhatsappFieldPrefix = (
    <div className="flex gap-2 items-center">
      <WhatsappPrefixIcon />
      <span className="text-primary-200">wa.me/</span>
    </div>
  );

  const YoutubeFieldPrefix = (
    <div className="flex gap-2 items-center">
      <YoutubePrefixIcon />
      <span className="text-primary-200">youtube.com/</span>
    </div>
  );

  return (
    <div className="pt-4 pr-4">
      <Alert
        message={<span className="font-semibold">{t('settings.companyDetails.title')}</span>}
        description={
          <p className="text-sm text-[#090A1A]">{t('settings.companyDetails.description')}</p>
        }
        type="info"
        className="mb-4"
        closable
      />
      <Form name="company-details-form" layout="vertical" className="custom-form" form={form}>
        <div className="flex gap-4 flex-col">
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Form.Item
              label={t('settings.companyDetails.companyName')}
              name="companyName"
              rules={[
                {
                  required: true,
                  message: t('settings.companyDetails.companyNameRequired'),
                },
              ]}
            >
              <Input
                placeholder={t('settings.companyDetails.companyNamePlaceholder')}
                maxLength={100}
              />
            </Form.Item>
            <Form.Item
              label={t('settings.companyDetails.companyShortName')}
              name="companyShortName"
            >
              <Input
                placeholder={t('settings.companyDetails.companyShortNamePlaceholder')}
                maxLength={30}
              />
            </Form.Item>
          </div>
          <Form.Item label={t('settings.companyDetails.companyTagline')} name="companyTagline">
            <Input
              placeholder="write a short message or slogan representing your brand’s identity"
              maxLength={255}
            />
          </Form.Item>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Form.Item
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('settings.companyDetails.companyWebsiteRequired'),
                },
                {
                  pattern: formErrorRegex.CheckIfValidURL,
                  message: t('settings.companyDetails.validUrlMessage'),
                },
              ]}
              name={'companyWebsite'}
              label={t('settings.companyDetails.companyWebsite')}
            >
              <Input
                min={5}
                maxLength={255}
                className="customer-general-input"
                addonBefore={
                  <Typography.Text
                    className="!text-black"
                    type="secondary"
                    defaultValue={'https://'}
                  >
                    https://
                  </Typography.Text>
                }
                placeholder={t('settings.companyDetails.companyWebsitePlaceholder')}
                allowClear
              />
            </Form.Item>
            <Form.Item
              rules={[
                {
                  required: true,
                  message: t('settings.companyDetails.emailRequired'),
                },
                {
                  type: 'email',
                  message: t('settings.companyDetails.validEmailMessage'),
                },
              ]}
              name={'supportEmail'}
              label={t('settings.companyDetails.supportEmail')}
            >
              <Input
                maxLength={255}
                placeholder={t('settings.companyDetails.supportEmailPlaceholder')}
              />
            </Form.Item>
          </div>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
            <Space.Compact className="combined-input">
              <Form.Item
                className="w-[100%]"
                dependencies={['countryCode']}
                validateFirst
                rules={[
                  {
                    required: true,
                    validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                  },
                  {
                    validator: (_, value) =>
                      validateMaskedInput(
                        value,
                        maskPhoneInput.length,
                        t('settings.companyDetails.validPhoneMessage')
                      ),
                  },
                ]}
                name="phoneNumber"
                label={t('settings.companyDetails.phoneNumber')}
              >
                <MaskedInput
                  ref={inputPhoneRef}
                  addonBefore={PhoneNumberAddonBefore}
                  className=" address-popup-maskedInput"
                  placeholder={t('settings.companyDetails.phoneNumberPlaceholder')}
                  mask={maskPhoneInput.mask}
                  onChange={(event) => form.setFieldValue('phoneNumber', event?.unmaskedValue)}
                />
              </Form.Item>
              <Form.Item name="phoneExtension" className="max-w-[100px]">
                <Input
                  placeholder={t('settings.companyDetails.phoneExtensionPlaceholder')}
                  maxLength={6}
                  onKeyDown={(event) => numberFieldValidator(event, {})}
                />
              </Form.Item>
            </Space.Compact>

            <Form.Item
              className="w-[100%]"
              dependencies={['countryCode']}
              validateFirst
              rules={[
                {
                  required: true,
                  validator: validateCountryAndValue(form, 'countryCode', 'phone number', true),
                },
                {
                  validator: (_, value) =>
                    validateMaskedInput(
                      value,
                      maskPhoneInput.length,
                      t('settings.companyDetails.validPhoneMessage')
                    ),
                },
              ]}
              name="faxNumber"
              label={t('settings.companyDetails.faxNumber')}
            >
              <MaskedInput
                ref={inputFaxRef}
                addonBefore={faxNumberAddonBefore}
                className=" address-popup-maskedInput"
                placeholder={t('settings.companyDetails.faxNumberPlaceholder')}
                mask={maskFaxInput.mask}
                onChange={(event) => form.setFieldValue('faxNumber', event?.unmaskedValue)}
              />
            </Form.Item>
          </div>
          <CustomDivider label={t('settings.companyDetails.socialMediaLinks')} />
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-3 3xl:grid-cols-3">
            <Form.Item label={t('settings.companyDetails.facebook')} name="facebook">
              <Input prefix={FacebookFieldPrefix} />
            </Form.Item>
            <Form.Item label={t('settings.companyDetails.x')} name="x">
              <Input prefix={XFieldPrefix} />
            </Form.Item>
            <Form.Item label={t('settings.companyDetails.linkedin')} name="linkedin">
              <Input prefix={LinkedInFieldPrefix} />
            </Form.Item>
          </div>
          <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-3 3xl:grid-cols-3">
            <Form.Item label={t('settings.companyDetails.youtube')} name="youtube">
              <Input prefix={YoutubeFieldPrefix} />
            </Form.Item>
            <Form.Item label={t('settings.companyDetails.instagram')} name="instagram">
              <Input prefix={InstagramFieldPrefix} />
            </Form.Item>
            <Form.Item label={t('settings.companyDetails.whatsapp')} name="whatsapp">
              <Input prefix={WhatsappFieldPrefix} />
            </Form.Item>
          </div>
          <div className="flex justify-start">
            <Button type="primary" htmlType="submit" className="h-[40px] px-8">
              {t('settings.companyDetails.save')}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default CompanyDetailsSettings;
