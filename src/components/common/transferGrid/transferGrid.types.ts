import { ISearchedProps } from '@/components/layout/SidebarLayout/searchFilter.types';
import { IServiceSearchHighlight } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { IColDef, TGridProps } from '@/types/AgGridTypes';
import { IRowNode, RowSelectedEvent } from 'ag-grid-community';

export interface ITransferGridProps<T> {
  colDefs: IColDef[];
  initialRowData: T[];
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  setSearchText: React.Dispatch<React.SetStateAction<IServiceSearchHighlight>>;
  searchText: IServiceSearchHighlight;
  availableGridHeader: string;
  selectedGridHeader: string;
  assignedServices: any[];
  availableGridSearchPlaceholder: string;
  selectedGridSearchPlaceholder: string;
  gridProps?: TGridProps;
  searchFilterProps?: ISearchedProps;
  availableGridEmptyStateTitle: string;
  availableGridEmptyStateDescription: string;
  selectedGridEmptyState: string;
  mainHeaderTitle: string;
  preventEditModeOnSave?: boolean;
  hideBackNavigation?: boolean;
  saveButtonText?: string;
  isSave?: boolean;
  onSave?: (selectedData: any[]) => Promise<void>;
  getSelectedData?: React.MutableRefObject<() => any[]>;
  isAdvanceFilter?: boolean;
  highlightMovedRows?: boolean;
  navigationAndRefreshBlocker?: boolean;
  setNewlySelectedOrders?: React.Dispatch<React.SetStateAction<any[]>>;
  getNavigationBlockerState?: (isBlocked: boolean) => void;
  isDataRequired?: boolean;
}
export interface ExtendedIRowNode extends IRowNode {
  selected: boolean;
}
export interface ExtendedRowSelectedEvent extends RowSelectedEvent {
  node: ExtendedIRowNode;
}
