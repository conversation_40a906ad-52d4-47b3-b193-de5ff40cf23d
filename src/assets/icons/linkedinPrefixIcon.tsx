const LinkedinPrefixIcon = () => {
  return (
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_14304_266273)">
        <path
          d="M13.6335 0H1.70046C1.42637 0 1.1635 0.108884 0.969689 0.302697C0.775876 0.496511 0.666992 0.759378 0.666992 1.03347V12.9665C0.666992 13.2406 0.775876 13.5035 0.969689 13.6973C1.1635 13.8911 1.42637 14 1.70046 14H13.6335C13.9076 14 14.1705 13.8911 14.3643 13.6973C14.5581 13.5035 14.667 13.2406 14.667 12.9665V1.03347C14.667 0.759378 14.5581 0.496511 14.3643 0.302697C14.1705 0.108884 13.9076 0 13.6335 0ZM4.83977 11.9262H2.73491V5.24028H4.83977V11.9262ZM3.78588 4.31375C3.54712 4.31241 3.31411 4.24036 3.11625 4.10672C2.9184 3.97307 2.76457 3.78381 2.67417 3.56282C2.58378 3.34183 2.56087 3.09901 2.60834 2.86502C2.65582 2.63102 2.77154 2.41633 2.94092 2.24804C3.11029 2.07975 3.32572 1.96541 3.56001 1.91944C3.79431 1.87346 4.03697 1.89793 4.25738 1.98974C4.47779 2.08155 4.66606 2.2366 4.79843 2.43531C4.9308 2.63402 5.00135 2.86749 5.00116 3.10625C5.00341 3.2661 4.97346 3.42477 4.91308 3.5728C4.85271 3.72083 4.76315 3.85519 4.64974 3.96787C4.53634 4.08055 4.4014 4.16925 4.25299 4.22867C4.10458 4.28809 3.94572 4.31703 3.78588 4.31375ZM12.5981 11.9321H10.4942V8.27944C10.4942 7.20222 10.0363 6.86972 9.44519 6.86972C8.82102 6.86972 8.20852 7.34028 8.20852 8.30667V11.9321H6.10366V5.24514H8.12783V6.17167H8.15505C8.35824 5.76042 9.06991 5.0575 10.1559 5.0575C11.3303 5.0575 12.5991 5.75458 12.5991 7.79625L12.5981 11.9321Z"
          fill="#0A66C2"
        />
      </g>
      <defs>
        <clipPath id="clip0_14304_266273">
          <rect width="14" height="14" fill="white" transform="translate(0.666992)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default LinkedinPrefixIcon;
