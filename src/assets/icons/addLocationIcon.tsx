const AddLocationIcon = ({ bool }: { bool: boolean }) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_5348_153212)">
        <path
          d="M11.0625 8.3125H6.9375C6.92444 8.3125 6.90878 8.3073 6.89449 8.29301C6.8802 8.27872 6.875 8.26306 6.875 8.25C6.875 8.23694 6.8802 8.22128 6.89449 8.20699C6.90878 8.1927 6.92444 8.1875 6.9375 8.1875H11.0625C11.0756 8.1875 11.0912 8.1927 11.1055 8.20699C11.1198 8.22128 11.125 8.23694 11.125 8.25C11.125 8.26306 11.1198 8.27872 11.1055 8.29301C11.0912 8.3073 11.0756 8.3125 11.0625 8.3125Z"
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M9 10.375C8.98694 10.375 8.97128 10.3698 8.95699 10.3555C8.9427 10.3412 8.9375 10.3256 8.9375 10.3125V6.1875C8.9375 6.17444 8.9427 6.15878 8.95699 6.14449C8.97128 6.1302 8.98694 6.125 9 6.125C9.01306 6.125 9.02872 6.1302 9.04301 6.14449C9.0573 6.15878 9.0625 6.17444 9.0625 6.1875V10.3125C9.0625 10.3256 9.0573 10.3412 9.04301 10.3555C9.02872 10.3698 9.01306 10.375 9 10.375Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
        <path
          d="M2.65498 6.35874L2.65511 6.35818C3.42365 2.97243 6.37567 1.4375 9.00001 1.4375H9.00001H9.00002H9.00002H9.00003H9.00003H9.00003H9.00004H9.00004H9.00005H9.00005H9.00005H9.00006H9.00006H9.00007H9.00007H9.00008H9.00008H9.00008H9.00009H9.00009H9.0001H9.0001H9.00011H9.00011H9.00012H9.00012H9.00013H9.00013H9.00014H9.00014H9.00015H9.00016H9.00016H9.00017H9.00018H9.00018H9.00019H9.0002H9.0002H9.00021H9.00021H9.00021H9.00022H9.00022H9.00023H9.00023H9.00024H9.00024H9.00024H9.00025H9.00025H9.00026H9.00026H9.00027H9.00027H9.00028H9.00028H9.00029H9.00029H9.00029H9.0003H9.0003H9.00031H9.00032H9.00032H9.00033H9.00033H9.00034H9.00034H9.00035H9.00035H9.00036H9.00036H9.00037H9.00038H9.00038H9.00039H9.00039H9.0004H9.00041H9.00041H9.00042H9.00042H9.00043H9.00044H9.00044H9.00045H9.00046H9.00046H9.00047H9.00048H9.00048H9.00049H9.0005H9.00051H9.00051H9.00052H9.00053H9.00054H9.00054H9.00055H9.00056H9.00057H9.00057H9.00058H9.00059H9.0006H9.00061H9.00061H9.00062H9.00063H9.00064H9.00065H9.00066H9.00066H9.00067H9.00068H9.00069H9.0007H9.00071H9.00072H9.00073H9.00074H9.00074H9.00075H9.00076H9.00077H9.00078H9.00079H9.0008H9.00081H9.00082H9.00083H9.00084H9.00085H9.00086H9.00087H9.00088H9.00089H9.0009H9.00092H9.00093H9.00094H9.00095H9.00096H9.00097H9.00098H9.00099H9.001H9.00102H9.00103H9.00104H9.00105H9.00106H9.00107H9.00109H9.0011H9.00111H9.00112H9.00113H9.00115H9.00116H9.00117H9.00119H9.0012H9.00121H9.00122H9.00124H9.00125H9.00126H9.00128H9.00129H9.0013H9.00132H9.00133H9.00135H9.00136H9.00137H9.00139H9.0014H9.00142H9.00143H9.00144H9.00146H9.00147H9.00149H9.0015H9.00152H9.00153H9.00155H9.00156H9.00158H9.0016H9.00161H9.00163H9.00164H9.00166H9.00168H9.00169H9.00171H9.00172H9.00174H9.00176H9.00177H9.00179H9.00181H9.00182H9.00184H9.00186H9.00188H9.00189H9.00191H9.00193H9.00195H9.00196H9.00198H9.002H9.00202H9.00204H9.00206H9.00207H9.00209H9.00211H9.00213H9.00215H9.00217H9.00219H9.00221H9.00223H9.00225H9.00227H9.00229H9.00231H9.00233H9.00235H9.00237H9.00239H9.00241H9.00243H9.00245H9.00247H9.00249H9.00251H9.00253H9.00255H9.00257H9.0026H9.00262H9.00264H9.00266H9.00268H9.00271H9.00273H9.00275H9.00277H9.0028H9.00282H9.00284H9.00286H9.00289H9.00291H9.00293H9.00296H9.00298H9.003H9.00303H9.00305H9.00308H9.0031H9.00313H9.00315H9.00317H9.0032H9.00322H9.00325H9.00327H9.0033H9.00332H9.00335H9.00338H9.0034H9.00343H9.00345H9.00348H9.00351H9.00353H9.00356H9.00359H9.00361H9.00364H9.00367H9.0037H9.00372H9.00375H9.00378H9.00381H9.00383H9.00386H9.00389H9.00392H9.00395H9.00398H9.004H9.00403H9.00406H9.00409H9.00412H9.00415H9.00418H9.00421H9.00424H9.00427H9.0043H9.00433H9.00436H9.00439H9.00442H9.00445H9.00448H9.00452H9.00455H9.00458H9.00461H9.00464H9.00467H9.00471H9.00474H9.00477H9.0048H9.00484H9.00487H9.0049H9.00493H9.00497H9.005H9.00503H9.00507H9.0051H9.00514H9.00517H9.0052H9.00524H9.00527H9.00531H9.00534H9.00538H9.00541H9.00545H9.00549H9.00552H9.00556H9.00559H9.00563H9.00566H9.0057H9.00574H9.00577H9.00581H9.00585H9.00589H9.00592H9.00596H9.006H9.00604H9.00607H9.00611H9.00615H9.00619H9.00623H9.00627H9.00631H9.00635H9.00639H9.00642H9.00646H9.0065H9.00654H9.00658H9.00662H9.00667H9.00671H9.00675H9.00679H9.00683H9.00687H9.00691H9.00695H9.007H9.00704H9.00708H9.00712H9.00716H9.00721H9.00725H9.00729H9.00734H9.00738H9.00742H9.00747H9.00751C11.6315 1.4375 14.5836 2.97209 15.3523 6.36538C16.2062 10.1377 13.9341 13.3552 11.7511 15.4569C10.9763 16.198 9.98377 16.57 9.00001 16.57C8.01625 16.57 7.02368 16.198 6.24887 15.4569C4.06615 13.3554 1.7942 10.1308 2.65498 6.35874ZM15.2328 6.38522L15.2324 6.38322C14.4135 2.81295 11.3312 1.5625 9.00001 1.5625C6.66845 1.5625 3.59352 2.81336 2.78254 6.38377C1.90976 10.1913 4.30991 13.4102 6.3434 15.3604C7.8345 16.7991 10.1732 16.799 11.6643 15.3602C13.6901 13.4102 16.0895 10.192 15.2328 6.38522Z"
          fill={bool ? '#2D3484' : '#20363F'}
          stroke={bool ? '#2D3484' : '#20363F'}
        />
      </g>
      <defs>
        <clipPath id="clip0_5348_153212">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default AddLocationIcon;
