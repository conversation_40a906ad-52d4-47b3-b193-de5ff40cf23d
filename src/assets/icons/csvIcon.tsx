const CsvIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.41667 3.20841C6.05199 3.20841 5.70226 3.35328 5.44439 3.61114C5.18653 3.86901 5.04167 4.21874 5.04167 4.58342V17.4167C5.04167 17.7814 5.18653 18.1312 5.44439 18.389C5.70226 18.6469 6.05199 18.7917 6.41667 18.7917H15.5833C15.948 18.7917 16.2977 18.6469 16.5556 18.389C16.8135 18.1312 16.9583 17.7814 16.9583 17.4167V8.62968C16.9583 8.50817 16.91 8.39155 16.824 8.30564L11.8612 3.34276C11.7752 3.25682 11.6587 3.20847 11.5372 3.20841H6.41667ZM4.79621 2.96296C5.22598 2.53319 5.80888 2.29175 6.41667 2.29175H11.5372C11.9018 2.29183 12.2515 2.43672 12.5093 2.69457M12.5093 2.69457L17.4721 7.65736C17.73 7.91516 17.8749 8.26486 17.875 8.62948V17.4167C17.875 18.0245 17.6336 18.6074 17.2038 19.0372C16.774 19.467 16.1911 19.7084 15.5833 19.7084H6.41667C5.80888 19.7084 5.22598 19.467 4.79621 19.0372C4.36644 18.6074 4.125 18.0245 4.125 17.4167V4.58342C4.125 3.97563 4.36644 3.39273 4.79621 2.96296"
        fill="#3F82EF"
        className="cell-icons"
      />
      <path
        d="M13.7331 10.1289L14.4167 12.4792H14.4427L15.1263 10.1289H16.1484L15.0482 13.4622H13.8112L12.7109 10.1289H13.7331Z"
        fill="#3F82EF"
        className="cell-icons"
      />
      <path
        d="M11.5651 11.1705C11.5564 11.062 11.5157 10.9773 11.443 10.9166C11.3714 10.8558 11.2624 10.8254 11.1159 10.8254C11.0226 10.8254 10.9461 10.8368 10.8864 10.8596C10.8278 10.8813 10.7844 10.9112 10.7562 10.9491C10.728 10.9871 10.7133 11.0305 10.7122 11.0793C10.7101 11.1195 10.7171 11.1558 10.7334 11.1884C10.7508 11.2199 10.7779 11.2486 10.8148 11.2747C10.8517 11.2996 10.8989 11.3224 10.9564 11.343C11.0139 11.3636 11.0822 11.3821 11.1615 11.3984L11.4349 11.4569C11.6194 11.496 11.7772 11.5476 11.9085 11.6116C12.0398 11.6756 12.1472 11.751 12.2308 11.8378C12.3143 11.9235 12.3757 12.0201 12.4147 12.1275C12.4549 12.2349 12.4755 12.3521 12.4766 12.4791C12.4755 12.6983 12.4207 12.8838 12.3122 13.0357C12.2037 13.1876 12.0485 13.3032 11.8467 13.3824C11.6459 13.4616 11.4045 13.5012 11.1224 13.5012C10.8327 13.5012 10.5799 13.4584 10.3639 13.3726C10.1491 13.2869 9.98199 13.1551 9.86263 12.9771C9.74436 12.7981 9.68468 12.5691 9.68359 12.2903H10.543C10.5484 12.3923 10.5739 12.478 10.6195 12.5474C10.665 12.6169 10.7291 12.6695 10.8115 12.7053C10.8951 12.7411 10.9944 12.759 11.1094 12.759C11.2059 12.759 11.2868 12.7471 11.3519 12.7232C11.417 12.6994 11.4664 12.6663 11.5 12.6239C11.5336 12.5816 11.551 12.5333 11.5521 12.4791C11.551 12.4281 11.5342 12.3836 11.5016 12.3456C11.4702 12.3066 11.4181 12.2718 11.3454 12.2415C11.2727 12.21 11.1745 12.1807 11.0508 12.1536L10.7188 12.0819C10.4236 12.0179 10.1909 11.9111 10.0205 11.7613C9.85124 11.6105 9.76714 11.4049 9.76823 11.1445C9.76714 10.9329 9.82357 10.7479 9.9375 10.5894C10.0525 10.4299 10.2115 10.3057 10.4144 10.2167C10.6184 10.1277 10.8522 10.0833 11.1159 10.0833C11.385 10.0833 11.6177 10.1283 11.8141 10.2183C12.0105 10.3084 12.1619 10.4354 12.2682 10.5992C12.3757 10.762 12.4299 10.9524 12.431 11.1705H11.5651Z"
        fill="#3F82EF"
        className="cell-icons"
      />
      <path
        d="M9.3584 11.3788H8.44043C8.43392 11.3029 8.41656 11.234 8.38835 11.1721C8.36122 11.1103 8.32324 11.0571 8.27441 11.0126C8.22667 10.967 8.16862 10.9323 8.10026 10.9084C8.0319 10.8835 7.95432 10.871 7.86751 10.871C7.7156 10.871 7.58702 10.9079 7.48177 10.9817C7.3776 11.0555 7.29839 11.1613 7.24414 11.2991C7.19097 11.4369 7.16439 11.6023 7.16439 11.7955C7.16439 11.9995 7.19151 12.1704 7.24577 12.3082C7.30111 12.4449 7.38086 12.548 7.48503 12.6174C7.58919 12.6858 7.71452 12.72 7.861 12.72C7.94455 12.72 8.01942 12.7097 8.08561 12.689C8.1518 12.6673 8.20931 12.6364 8.25814 12.5963C8.30697 12.5561 8.34657 12.5078 8.37695 12.4514C8.40842 12.3939 8.42958 12.3293 8.44043 12.2577L9.3584 12.2642C9.34755 12.4053 9.30794 12.5491 9.23958 12.6956C9.17122 12.841 9.07411 12.9755 8.94824 13.0992C8.82346 13.2218 8.66884 13.3206 8.48437 13.3954C8.29991 13.4703 8.08561 13.5077 7.84147 13.5077C7.53548 13.5077 7.26096 13.4421 7.0179 13.3108C6.77593 13.1795 6.58442 12.9864 6.44336 12.7314C6.30339 12.4764 6.2334 12.1644 6.2334 11.7955C6.2334 11.4244 6.30501 11.1119 6.44824 10.858C6.59147 10.603 6.78461 10.4104 7.02767 10.2802C7.27072 10.1489 7.54199 10.0833 7.84147 10.0833C8.05197 10.0833 8.24566 10.112 8.42253 10.1695C8.59939 10.227 8.75456 10.3111 8.88802 10.4218C9.02148 10.5314 9.12891 10.6665 9.21029 10.8271C9.29167 10.9877 9.34104 11.1716 9.3584 11.3788Z"
        fill="#3F82EF"
        className="cell-icons"
      />
    </svg>
  );
};

export default CsvIcon;
