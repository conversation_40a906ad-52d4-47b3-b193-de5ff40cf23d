const ExcelIcon = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.41667 3.20841C6.05199 3.20841 5.70226 3.35328 5.44439 3.61114C5.18653 3.86901 5.04167 4.21874 5.04167 4.58342V17.4167C5.04167 17.7814 5.18653 18.1312 5.44439 18.389C5.70226 18.6469 6.05199 18.7917 6.41667 18.7917H15.5833C15.948 18.7917 16.2977 18.6469 16.5556 18.389C16.8135 18.1312 16.9583 17.7814 16.9583 17.4167V8.62968C16.9583 8.50817 16.91 8.39155 16.824 8.30564L11.8612 3.34276C11.7752 3.25682 11.6587 3.20847 11.5372 3.20841H6.41667ZM4.79621 2.96296C5.22598 2.53319 5.80888 2.29175 6.41667 2.29175H11.5372C11.9018 2.29183 12.2515 2.43672 12.5093 2.69457M12.5093 2.69457L17.4721 7.65736C17.73 7.91516 17.8749 8.26486 17.875 8.62948V17.4167C17.875 18.0245 17.6336 18.6074 17.2038 19.0372C16.774 19.467 16.1911 19.7084 15.5833 19.7084H6.41667C5.80888 19.7084 5.22598 19.467 4.79621 19.0372C4.36644 18.6074 4.125 18.0245 4.125 17.4167V4.58342C4.125 3.97563 4.36644 3.39273 4.79621 2.96296"
        fill="#12B76A"
        className="cell-icons"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M5.95833 10.0834H16.0417V17.4167H5.95833V10.0834ZM6.875 11.0001V13.2917H9.16667V11.0001H6.875ZM10.0833 11.0001V13.2917H15.125V11.0001H10.0833ZM15.125 14.2084H10.0833V16.5001H15.125V14.2084ZM9.16667 16.5001V14.2084H6.875V16.5001H9.16667Z"
        fill="#12B76A"
        className="cell-icons"
      />
    </svg>
  );
};

export default ExcelIcon;
