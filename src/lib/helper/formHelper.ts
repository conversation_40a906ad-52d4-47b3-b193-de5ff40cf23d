const normalizeObject = (obj: any): any => {
  if (typeof obj === 'number') {
    return String(obj);
  }

  if (obj === null || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(normalizeObject);
  }

  // sort keys and recurse
  return Object.keys(obj)
    .sort()
    .reduce(
      (acc, key) => {
        acc[key] = normalizeObject(obj[key]);
        return acc;
      },
      {} as Record<string, any>
    );
};

export const isFormChangedHandler = (
  initialValue: Record<string, any>,
  currentValue: Record<string, any>,
  fieldsWithInitialValues: string[] = []
): boolean => {
  const isInitialValueEmpty = Object.keys(initialValue).length < 1;
  const isAddFormChanged = Object.keys(currentValue).some((key) => {
    return !fieldsWithInitialValues.includes(key) && currentValue[key];
  });

  const normalizedInitial = normalizeObject(initialValue);
  const normalizedCurrent = normalizeObject(currentValue);

  fieldsWithInitialValues.forEach((key) => {
    delete normalizedInitial[key];
    delete normalizedCurrent[key];
  });

  return isInitialValueEmpty
    ? isAddFormChanged
    : JSON.stringify(normalizedInitial) !== JSON.stringify(normalizedCurrent);
};

export const numberParser = (value: string | undefined) => {
  if (!value) {
    return 0;
  }
  const parsedValue = parseFloat(value.replace(/\$\s?|(,*)/g, ''));
  return isNaN(parsedValue) ? 0 : parsedValue;
};

export const isJSON = (str: string) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
