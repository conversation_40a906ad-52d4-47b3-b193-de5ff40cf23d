export const downloadFromBlob = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
};

export const blobToUrlNavigation = (blob: Blob) => {
  const url = window.URL.createObjectURL(blob);
  window.open(url, '_blank');
};