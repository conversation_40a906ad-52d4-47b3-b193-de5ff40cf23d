export const filterableModules = {
  customer: {
    searchTerms: [
      'companyName',
      'accountNumber',
      'contactName',
      'addressLine1',
      'city',
      'phoneNumber',
      'email',
      'postalCode',
    ],
    advanceFilter: [
      'companyName',
      'accountNumber',
      'contactName',
      'addressLine1',
      'city',
      'phoneNumber',
      'email',
      'postalCode',
    ],
    sortable: [
      'companyName',
      'accountNumber',
      'contactName',
      'addressLine1',
      'city',
      'phoneNumber',
      'email',
      'postalCode',
      'createdAt',
    ],
  },
  invoices: {
    // searchTerms: ['status', 'invoiceNumber', 'dueDate', 'createdAt', 'totalAmount', 'aging'],
    advanceFilter: [
      'status',
      'invoiceNumber',
      'dueDate',
      'createdAt',
      'totalAmount',
      'customerName',
    ],
    sortable: [
      'status',
      'invoiceNumber',
      'companyName',
      'customerName',
      'dueDate',
      'createdAt',
      'totalAmount',
      'companyName',
      'customerName',
    ],
  },
  address: {
    searchTerms: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
    ],
    advanceFilter: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
    ],
    sortable: [
      'name',
      'companyName',
      'addressLine1',
      'addressLine2',
      'city',
      'postalCode',
      'phoneNumber',
      'email',
      'createdAt',
    ],
  },
  vehicle: {
    searchTerms: ['fleetId', 'make', 'model', 'licensePlate'],
    advanceFilter: [
      'fleetId',
      'make',
      'model',
      'year',
      'licensePlate',
      'maxWeight',
      'currentOdometer',
      'createdAt',
    ],
    sortable: ['fleetId', 'make', 'model', 'licensePlate', 'createdAt'],
  },
  timeClockSession: {
    searchTerms: [],
    advanceFilter: ['startTime', 'endTime', 'distanceTraveled'],
    sortable: ['startTime', 'endTime', 'createdAt'],
  },
  priceSets: {
    searchTerms: ['name', 'internalName'],
    advanceFilter: ['name', 'internalName'],
    sortable: ['name', 'internalName', 'createdAt'],
  },
  priceModifier: {
    searchTerms: ['name', 'fieldName'],
    advanceFilter: ['name', 'fieldName'],
    sortable: ['name', 'fieldName', 'createdAt', 'type'],
  },
  zone: {
    searchTerms: ['name', 'notes'],
    advanceFilter: ['name', 'notes', 'postalCodes'],
    sortable: ['name', 'notes', 'createdAt'],
  },
  contact: {
    searchTerms: ['name', 'email', 'phoneNumber'],
    advanceFilter: [
      'name',
      'email',
      'phoneNumber',
      'phoneCountryCode',
      'isPrimary',
      'emailVerified',
      'userId',
      'createdAt',
      'updatedAt',
    ],
    sortable: [
      'name',
      'email',
      'phoneNumber',
      'phoneCountryCode',
      'isPrimary',
      'emailVerified',
      'createdAt',
      'updatedAt',
      'isDeleted',
    ],
  },
  payment: {
    // searchTerms: ['referenceNumber', 'date', 'invoiceNumber', 'type', 'created'],
    advanceFilter: [
      'referenceNumber',
      'invoiceNumber',
      'amountPaid',
      'type',
      'created',
      'customerName',
    ],
    sortable: ['customer', 'referenceNumber', 'date', 'amount', 'invoiceNumber', 'type', 'created'],
  },
  order: {
    searchTerms: ['trackingNumber', 'referenceNumber', 'description', 'comments', 'internalNotes'],
    advanceFilter: [
      'trackingNumber',
      'referenceNumber',
      'status',
      'customerId',
      'scheduledCollectionTime',
      'scheduledDeliveryTime',
      'billingStatus',
      'paymentStatus',
      'collectionAddressId',
      'deliveryAddressId',
      'totalPrice',
      'createdAt',
      'updatedAt',
    ],
    sortable: [
      'trackingNumber',
      'referenceNumber',
      'status',
      'scheduledCollectionTime',
      'scheduledDeliveryTime',
      'totalPrice',
      'billingStatus',
      'paymentStatus',
      'createdAt',
      'updatedAt',
    ],
  },
};
