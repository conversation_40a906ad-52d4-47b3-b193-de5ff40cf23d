.custom-select-dropdown,
.custom-select-selection-item,
.custom-select-selection-search-input {
  font-family: var(--font-family) !important;
}

.custom-select-selector {
  border: 1px solid var(--input-border) !important;
  min-height: 40px !important;
  border-radius: 6px !important;
}
.custom-select-multiple .custom-select-selection-wrap {
  align-self: normal !important;
}
.custom-select-outlined.custom-select-multiple .custom-select-selection-item {
  background-color: var(--primary-25) !important;
  color: var(--primary-600) !important;
  border: 1px solid var(--primary-100) !important;
  font-weight: 400;
  font-size: 14px;
  height: auto !important;
  padding: 3px 8px !important;
  border-radius: 6px !important;
}
.custom-select-selection-item-remove {
  color: var(--primary-200) !important;
}
.custom-date-picker,
.custom-time-picker {
  height: 40px !important;
  width: 100% !important;
}
.custom-time-picker .custom-time-picker-input > input,
.custom-date-picker .custom-time-picker-input > input {
  font-family: var(--font-family);
}
.custom-date-picker-dropdown
  .custom-date-picker-cell-in-view.custom-date-picker-cell-selected:not(
    .custom-date-picker-cell-disabled
  )
  .custom-date-picker-cell-inner {
  background-color: var(--primary-600);
}
.custom-date-picker-dropdown
  .custom-date-picker-cell-in-view.custom-date-picker-cell-range-end:not(
    .custom-date-picker-cell-disabled
  )
  .custom-date-picker-cell-inner,
.custom-date-picker-dropdown
  .custom-date-picker-cell-in-view.custom-date-picker-cell-range-start:not(
    .custom-date-picker-cell-disabled
  )
  .custom-date-picker-cell-inner {
  background-color: var(--primary-600);
}
.custom-date-picker-dropdown
  .custom-date-picker-cell-in-view.custom-date-picker-cell-today
  .custom-date-picker-cell-inner::before {
  border: 1px solid var(--primary-600);
}
.custom-date-picker-dropdown .custom-date-picker-header-view > button:hover,
.custom-date-picker-dropdown a:hover,
.custom-time-picker-dropdown a:hover {
  color: var(--primary-600) !important;
}
.custom-date-picker-ok .ant-btn-variant-solid:not(.ant-btn-variant-solid:disabled) {
  background-color: var(--primary-600);
}

.avoid-tab-position .ant-tabs-content {
  position: inherit !important;
}
.custom-select-single {
  height: 100% !important;
}
.custom-antd-outlined,
.custom-antd-outlined:hover {
  @apply !rounded-lg !border-primary-200 !text-inherit;
}

.ant-alert-with-description {
  padding: 12px !important;
}

.ant-input-affix-wrapper > input.ant-input {
  height: auto !important;
}

.custom-form .ant-input-affix-wrapper {
  height: 40px !important;
}

.custom-form .doNotWrap .ant-radio-group {
  display: flex;
}
