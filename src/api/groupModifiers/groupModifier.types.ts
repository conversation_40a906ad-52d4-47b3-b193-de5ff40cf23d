import { ModifierGroupBehavior } from '../priceModifier/priceModifier.types';

export interface IGroupMember {
  id: string;
  isGroup: boolean;
  name?: string;
}
export interface CreatePriceModifierGroupDto {
  name: string;
  description: string;
  behavior: ModifierGroupBehavior;
  members: IGroupMember[];
  isGroup?: boolean;
  multiplyAgainField?: string;
}

export interface IPriceModifierGroup extends CreatePriceModifierGroupDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}
export interface GroupModifiersPaginatedResponse {
  data: IPriceModifierGroup[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
