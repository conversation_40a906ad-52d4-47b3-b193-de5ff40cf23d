export interface CreateZoneTableDto {
  name: string;
  zoneTableValues: CreateZoneTableValueDto[];
}

export interface CreateZoneTableValueDto {
  originZoneId: string;
  destinationZoneId: string;
  value: number | null;
}

export interface ZoneTable extends CreateZoneTableDto {
  /** Unique identifier for the zone */
  id: string;
  /** Timestamp when the zone was created */
  createdAt: string;
  /** Timestamp when the zone was last updated */
  updatedAt: string;
  /** User who created the zone */
  createdBy: string;
  /** User who last updated the zone */
  updatedBy: string;
}

export interface UpdateZoneTableValueDto {
  originZoneId: string;
  destinationZoneId: string;
  value: string;
  id: string;
}

/**
 * Paginated response interface for zones
 */
export interface ZoneTablePaginatedResponse {
  /** Total number of zones */
  total: number;
  /** Current page number */
  pageNumber: number;
  /** Number of items per page */
  pageSize: number;
  /** Array of zone data */
  data: ZoneTable[];
}
