export enum InvoiceStatus {
    Draft = 'Draft',
    Sent = 'Sent',
    Paid = 'Paid',
    PartialPaid = 'PartialPaid',
    Void = 'Void',
    Refunded = 'Refunded',
}
export enum InvoiceAging {
    ZeroToThirty = '0-30',
    ThirtyOneToSixty = '31-60',
    SixtyOneToNinety = '61-90',
    NinetyOnePlus = '91+',
}
export interface ICreateInvoiceDto {
    customerId: string
    invoiceDate: string
    dueDate: string
    status: InvoiceStatus
    notes?: string
    orderIds?: string[]
    invoiceNumber: string

}
export interface IResponseInvoiceDto extends ICreateInvoiceDto {
    id: string
    tenantId: string
    customerId: string
    customerName?: string
    customerEmail?: string
    customerPhoneNumber?: string
    orderTrackingNumber?: string
    subtotal: number
    taxAmount: number
    total: number
    totalAmount: number
    paidAmount: number
    pendingAmount: number
    refundAmount: number
    aging?: InvoiceAging
    termsConditions?: string
    createdBy?: string
    updatedBy?: string
    createdAt: string
    updatedAt: string
    deletedAt?: string
    orders?: any[]
}

export interface IInvoiceListingDto {
  data: IResponseInvoiceDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}