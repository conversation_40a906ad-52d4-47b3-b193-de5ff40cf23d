import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { InvoiceService } from './invoices.service';
import { ICreateInvoiceDto,  IInvoiceListingDto } from './invoices.types';

export const invoicesService = new InvoiceService(apiClient.getAxiosInstance());

export const invoicesHook = createEntityHooks<
    IInvoiceListingDto,
    ICreateInvoiceDto,
    ICreateInvoiceDto
>(QueryHookKey.invoices, invoicesService);
