import { IResponseInvoiceDto } from "../invoices/invoices.types";

export enum PaymentMethodEnum {
    Cash = "Cash",
    BankTransfer = "BankTransfer",
    CreditCard = "CreditCard",
    DebitCard = "DebitCard",
    Other = "Other",
}
export interface CreatePaymentDto {
    customerId: string;
    receivedDate: string;
    invoices: string[];
    referenceNumber: string;
    paymentMethod: PaymentMethodEnum;
    amountPaid: number;
    note?: string;
}

export interface PaymentResponseDto extends CreatePaymentDto {
    invoiceDetails: IResponseInvoiceDto[];
    id: string;

    tenantId: string;

    amountAdjust: number;

    amountBalance: number;

    status: string;

    metadata?: Record<string, any>;

    isDeleted: boolean;

    deletedAt?: string;

    createdAt: string;

    updatedAt: string;

    createdBy?: string;

    updatedBy?: string;
}
export interface IPayment {
    data: PaymentResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}
