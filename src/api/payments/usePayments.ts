import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { CreatePaymentDto, IPayment } from './payments.types';
import { PaymentsService } from './payments.service';

const paymentService = new PaymentsService(apiClient.getAxiosInstance());

export const paymentsHook = createEntityHooks<
    IPayment,
    CreatePaymentDto,
    CreatePaymentDto
>(QueryHookKey.payments, paymentService);
