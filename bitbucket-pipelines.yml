definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-stores

pipelines:
  branches:
    develop:
      - step:
          name: Build for dev
          size: 2x
          services:
            - docker
          image: node:20.17.0
          caches:
            - pnpm
          script:
            - corepack enable
            - corepack prepare pnpm@9.15.1 --activate
            - pnpm install
            - pnpm run build:dev
            - export IMAGE_NAME=blobstation/lumingo_admin_develop:$BITBUCKET_COMMIT
            - docker build -t $IMAGE_NAME -f Dockerfile.dev .
            - docker login --username $DOCKER_HUB_USERNAME --password $DOCKER_HUB_PASSWORD
            - docker push $IMAGE_NAME

      - step:
          name: Deploy to Dev Environment
          deployment: Dev
          script:
            - pipe: atlassian/ssh-run:0.2.2
              variables:
                SSH_USER: $SSH_USER
                SERVER: $SSH_HOST
                COMMAND: |
                  cd /root/lumingo/dev;
                  LUMINGO_ADMIN_VERSION=$BITBUCKET_COMMIT docker compose up -d --build --no-deps lumingo_admin_develop
